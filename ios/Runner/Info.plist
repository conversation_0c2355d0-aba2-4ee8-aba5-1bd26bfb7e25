<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>
    <key>CADisableMinimumFrameDurationOnPhone - 2</key>
    <true/>
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>Meshii</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>Meshii</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>$(FLUTTER_BUILD_NAME)</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>$(FLUTTER_BUILD_NUMBER)</string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>LSSupportsOpeningDocumentsInPlace</key>
    <true/>
    <key>NSBonjourServices</key>
    <array>
      <string>_mp-connection._tcp</string>
    </array>
    <key>NSCameraUsageDescription</key>
    <string>Camera usage description</string>
    <key>NSFaceIDUsageDescription</key>
    <string>Toii Social want to use it login</string>
    <key>NSLocalNetworkUsageDescription</key>
    <string>Reason for using Bonjour that the user can understand</string>
    <key>NSLocationAlwaysUsageDescription</key>
    <string>Your location is required for xyz benefits for you</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>Your location is required for xyz benefits for you</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>Photo Library Access Warning</string>
    <key>ITSAppUsesNonExemptEncryption</key>
    <false/>
    <key>UIApplicationSupportsIndirectInputEvents</key>
    <true/>
    <key>UIFileSharingEnabled</key>
    <true/>
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>
    <key>UIMainStoryboardFile</key>
    <string>Main</string>
    <key>UIRequiresPersistentWiFi</key>
    <true/>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
  </dict>
</plist>