package com.toii.ai.toii_xmtp_flutter.handlers

import android.util.Log
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.plugin.common.EventChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.collect
import org.xmtp.android.library.Client
import org.xmtp.android.library.Dm
import org.xmtp.android.library.Group
import org.xmtp.android.library.libxmtp.DecodedMessage
import org.xmtp.android.library.codecs.id

class MessageHandler {
    
    fun sendMessage(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch
                val conversationId = call.argument<String>("conversationId")
                    ?: return@launch result.error("INVALID_ARGUMENT", "conversationId is required", null)
                val content = call.argument<String>("content")
                    ?: return@launch result.error("INVALID_ARGUMENT", "content is required", null)

                Log.d("XMTP", "Sending message to conversation: $conversationId")

                val conversation = findConversationWithSync(client, conversationId)

                if (conversation == null) {
                    Log.e("XMTP", "Conversation not found after sync attempts: $conversationId")
                    return@launch result.error("CONVERSATION_NOT_FOUND", "Conversation not found: $conversationId", null)
                }

                Log.d("XMTP", "Syncing conversation before sending message: $conversationId")
                when (conversation) {
                    is org.xmtp.android.library.Dm -> conversation.sync()
                    is org.xmtp.android.library.Group -> conversation.sync()
                }

                val messageId = when (conversation) {
                    is org.xmtp.android.library.Dm -> {
                        Log.d("XMTP", "Sending DM message: $content")
                        conversation.send(content)
                    }
                    is org.xmtp.android.library.Group -> {
                        Log.d("XMTP", "Sending Group message: $content")
                        conversation.send(content)
                    }
                    else -> {
                        return@launch result.error("UNSUPPORTED_CONVERSATION_TYPE", "Unsupported conversation type", null)
                    }
                }

                Log.d("XMTP", "Message sent successfully with ID: $messageId")
                result.success(messageId)
            } catch (e: Exception) {
                Log.e("XMTP", "Failed to send message: ${e.message}", e)
                result.error("SEND_MESSAGE_ERROR", "Failed to send message: ${e.message}", e.toString())
            }
        }
    }
    
    fun getMessages(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch
                val conversationId = call.argument<String>("conversationId")
                    ?: return@launch result.error("INVALID_ARGUMENT", "conversationId is required", null)

                Log.d("XMTP", "Getting messages for conversation: $conversationId")

                val conversation = findConversationWithSync(client, conversationId)

                if (conversation == null) {
                    Log.e("XMTP", "Conversation not found after sync attempts: $conversationId")
                    return@launch result.error("CONVERSATION_NOT_FOUND", "Conversation not found: $conversationId", null)
                }

                Log.d("XMTP", "Syncing conversation before getting messages: $conversationId")
                when (conversation) {
                    is org.xmtp.android.library.Dm -> conversation.sync()
                    is org.xmtp.android.library.Group -> conversation.sync()
                }

                val limit = call.argument<Int>("limit") ?: 50
                val beforeNs = call.argument<Long>("beforeNs")
                val afterNs = call.argument<Long>("afterNs")

                Log.d("XMTP", "Retrieving messages with limit: $limit")

                val messages = when (conversation) {
                    is org.xmtp.android.library.Dm -> {
                        conversation.messages(
                            limit = limit,
                            beforeNs = beforeNs,
                            afterNs = afterNs
                        )
                    }
                    is org.xmtp.android.library.Group -> {
                        conversation.messages(
                            limit = limit,
                            beforeNs = beforeNs,
                            afterNs = afterNs
                        )
                    }
                    else -> {
                        return@launch result.error("UNSUPPORTED_CONVERSATION_TYPE", "Unsupported conversation type", null)
                    }
                }

                val messageList = messages.mapNotNull { message ->
                    try {
                        if (message.id.isNullOrEmpty()) {
                            Log.w("XMTP", "Skipping message with null/empty ID")
                            return@mapNotNull null
                        }

                        mapOf(
                            "id" to message.id,
                            "conversationId" to conversationId,
                            "senderInboxId" to (message.senderInboxId ?: ""),
                            "content" to (message.body ?: ""),
                            "contentType" to (message.encodedContent.type.id ?: "text/plain"),
                            "sentAt" to message.sentAt.time,
                            "deliveryStatus" to when (message.deliveryStatus) {
                                org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.PUBLISHED -> "published"
                                org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.UNPUBLISHED -> "unpublished"
                                org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.FAILED -> "failed"
                                else -> "published"
                            }
                        )
                    } catch (e: Exception) {
                        Log.e("XMTP", "Error converting message: ${e.message}", e)
                        null
                    }
                }

                Log.d("XMTP", "Retrieved ${messageList.size} messages")
                result.success(messageList)
            } catch (e: Exception) {
                Log.e("XMTP", "Error getting messages: ${e.message}", e)
                result.error("GET_MESSAGES_ERROR", "Failed to get messages: ${e.message}", e.toString())
            }
        }
    }

    fun listMessages(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch

                result.success(emptyList<Map<String, Any>>())
            } catch (e: Exception) {
                result.error("LIST_MESSAGES_ERROR", "Failed to list messages: ${e.message}", e.toString())
            }
        }
    }
    
    private fun getClientFromCall(call: MethodCall, result: Result): Client? {
        return ClientHandler.getClient() ?: run {
            result.error("NO_CLIENT", "Client not initialized", null)
            null
        }
    }

    private suspend fun findConversationWithSync(client: Client, conversationId: String): Any? {
        Log.d("XMTP", "Finding conversation with sync: $conversationId")

        var conversation = findConversationInLists(client, conversationId)
        if (conversation != null) {
            Log.d("XMTP", "Found conversation in current lists: $conversationId")
            return conversation
        }

        try {
            Log.d("XMTP", "Syncing conversations before retry: $conversationId")
            client.conversations.sync()
            conversation = findConversationInLists(client, conversationId)
            if (conversation != null) {
                Log.d("XMTP", "Found conversation after sync: $conversationId")
                return conversation
            }
        } catch (e: Exception) {
            Log.e("XMTP", "Error syncing conversations: ${e.message}")
        }

        try {
            Log.d("XMTP", "Trying native findConversation method: $conversationId")
            val nativeConversation = client.conversations.findConversation(conversationId)
            if (nativeConversation != null) {
                Log.d("XMTP", "Found conversation using native method: $conversationId")
                return when (nativeConversation) {
                    is org.xmtp.android.library.Conversation.Dm -> nativeConversation.dm
                    is org.xmtp.android.library.Conversation.Group -> nativeConversation.group
                }
            }
        } catch (e: Exception) {
            Log.e("XMTP", "Error using native findConversation: ${e.message}")
        }

        Log.w("XMTP", "Conversation not found after all attempts: $conversationId")
        return null
    }

    private suspend fun findConversationInLists(client: Client, conversationId: String): Any? {
        return try {
            client.conversations.listDms().find { it.id == conversationId }
                ?: client.conversations.listGroups().find { it.id == conversationId }
        } catch (e: Exception) {
            Log.e("XMTP", "Error searching conversation lists: ${e.message}")
            null
        }
    }
}


