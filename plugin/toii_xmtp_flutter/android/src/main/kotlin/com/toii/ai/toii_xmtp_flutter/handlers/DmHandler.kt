package com.toii.ai.toii_xmtp_flutter.handlers

import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel.Result
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.xmtp.android.library.Client
import org.xmtp.android.library.Dm

class DmHandler {

    fun findOrCreateDm(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch
                val targetInboxId = call.argument<String>("targetInboxId")
                    ?: return@launch result.error("INVALID_ARGUMENT", "targetInboxId is required", null)

                android.util.Log.d("XMTP", "Finding or creating DM with targetInboxId: $targetInboxId")

                val dm = client.conversations.findOrCreateDm(targetInboxId)

                val dmData = mapOf(
                    "id" to dm.id,
                    "topic" to dm.topic,
                    "peerInboxId" to dm.peerInboxId,
                    "createdAt" to dm.createdAt.time,
                    "consentState" to dm.consentState().name.lowercase(),
                    "isActive" to true, // DMs are always active when created/found
                    "peerAddress" to null // Will be null for XMTP v3 DMs using inbox IDs
                )

                android.util.Log.d("XMTP", "DM created/found successfully: ${dm.id}")
                result.success(dmData)
            } catch (e: Exception) {
                android.util.Log.e("XMTP", "Failed to find or create DM: ${e.message}", e)
                result.error("FIND_OR_CREATE_DM_ERROR", "Failed to find or create DM: ${e.message}", e.toString())
            }
        }
    }

    fun listDms(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch

                android.util.Log.d("XMTP", "Listing DMs")

                val dms = client.conversations.listDms()
                val dmList = dms.map { dm ->
                    mapOf(
                        "id" to dm.id,
                        "topic" to dm.topic,
                        "peerInboxId" to dm.peerInboxId,
                        "createdAt" to dm.createdAt.time,
                        "consentState" to dm.consentState().name.lowercase(),
                        "isActive" to true, // DMs are always active when listed
                        "peerAddress" to null // Will be null for XMTP v3 DMs using inbox IDs
                    )
                }

                android.util.Log.d("XMTP", "Found ${dmList.size} DMs")
                result.success(dmList)
            } catch (e: Exception) {
                android.util.Log.e("XMTP", "Failed to list DMs: ${e.message}", e)
                result.error("LIST_DMS_ERROR", "Failed to list DMs: ${e.message}", e.toString())
            }
        }
    }
    
    private fun getClientFromCall(call: MethodCall, result: Result): Client? {
        return ClientHandler.getClient() ?: run {
            result.error("NO_CLIENT", "Client not initialized", null)
            null
        }
    }
}
