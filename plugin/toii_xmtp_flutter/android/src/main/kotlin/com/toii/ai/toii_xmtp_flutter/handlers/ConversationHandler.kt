package com.toii.ai.toii_xmtp_flutter.handlers

import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.plugin.common.EventChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.collect
import org.xmtp.android.library.Client
import org.xmtp.android.library.Conversation
import org.xmtp.android.library.Group
import org.xmtp.android.library.Dm

class ConversationHandler {
    
    fun listConversations(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch

                val includeGroups = call.argument<Boolean>("includeGroups") ?: true
                val includeDms = call.argument<Boolean>("includeDms") ?: true

                android.util.Log.d("XMTP", "Listing conversations - Groups: $includeGroups, DMs: $includeDms")

                val conversations = mutableListOf<Map<String, Any?>>()

                // Sync conversations first to ensure we have latest data
                try {
                    client.conversations.sync()
                    android.util.Log.d("XMTP", "Conversations synced successfully")
                } catch (e: Exception) {
                    android.util.Log.w("XMTP", "Failed to sync conversations: ${e.message}")
                    // Continue anyway - we'll work with cached data
                }

                // Add DMs if requested
                if (includeDms) {
                    try {
                        val dms = client.conversations.listDms()
                        android.util.Log.d("XMTP", "Found ${dms.size} DMs")

                        for (dm in dms) {
                            val dmData = mapOf<String, Any?>(
                                "id" to dm.id,
                                "topic" to dm.topic,
                                "peerInboxId" to dm.peerInboxId,
                                "createdAt" to dm.createdAt.time,
                                "consentState" to dm.consentState().name.lowercase(),
                                "isActive" to dm.isActive(),
                                "type" to "dm",
                                "peerAddress" to null // Will be null for XMTP v3 DMs using inbox IDs
                            )
                            conversations.add(dmData)
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("XMTP", "Error listing DMs: ${e.message}")
                    }
                }

                // Add Groups if requested
                if (includeGroups) {
                    try {
                        val groups = client.conversations.listGroups()
                        android.util.Log.d("XMTP", "Found ${groups.size} groups")

                        for (group in groups) {
                            val members = group.members()
                            val groupData = mapOf<String, Any?>(
                                "id" to group.id,
                                "topic" to group.topic,
                                "name" to (group.name ?: ""),
                                "description" to (group.description ?: ""),
                                "imageUrl" to (group.imageUrl ?: ""),
                                "createdAt" to group.createdAt.time,
                                "consentState" to group.consentState().name.lowercase(),
                                "isActive" to group.isActive(),
                                "type" to "group",
                                "memberCount" to members.size,
                                "memberInboxIds" to members.map { it.inboxId },
                                "adminInboxIds" to group.listAdmins(),
                                "superAdminInboxIds" to group.listSuperAdmins(),
                                "creatorInboxId" to group.addedByInboxId()
                            )
                            conversations.add(groupData)
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("XMTP", "Error listing groups: ${e.message}")
                    }
                }

                android.util.Log.d("XMTP", "Returning ${conversations.size} total conversations")
                result.success(conversations)
            } catch (e: Exception) {
                android.util.Log.e("XMTP", "Failed to list conversations: ${e.message}", e)
                result.error("LIST_CONVERSATIONS_ERROR", "Failed to list conversations: ${e.message}", e.toString())
            }
        }
    }
    
    fun syncConversations(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch

                android.util.Log.d("XMTP", "Syncing conversations")

                // Sync conversations from the network
                client.conversations.sync()

                android.util.Log.d("XMTP", "Conversations synced successfully")
                result.success(true)
            } catch (e: Exception) {
                android.util.Log.e("XMTP", "Failed to sync conversations: ${e.message}", e)
                result.error("SYNC_CONVERSATIONS_ERROR", "Failed to sync conversations: ${e.message}", e.toString())
            }
        }
    }
    
    fun streamConversations(call: MethodCall, result: Result, eventChannel: EventChannel, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch
                
                val includeGroups = call.argument<Boolean>("includeGroups") ?: true
                val includeDms = call.argument<Boolean>("includeDms") ?: true
                
                // TODO: Implement actual conversation streaming
                
                result.success(true)
            } catch (e: Exception) {
                result.error("STREAM_CONVERSATIONS_ERROR", "Failed to setup conversation stream: ${e.message}", e.toString())
            }
        }
    }
    
    private fun getClientFromCall(call: MethodCall, result: Result): Client? {
        return ClientHandler.getClient() ?: run {
            result.error("NO_CLIENT", "Client not initialized", null)
            null
        }
    }
    
    // TODO: Implement these methods when we have the correct API
}


