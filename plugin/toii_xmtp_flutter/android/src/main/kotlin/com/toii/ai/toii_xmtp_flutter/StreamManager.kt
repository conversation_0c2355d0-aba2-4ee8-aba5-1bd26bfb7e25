package com.toii.ai.toii_xmtp_flutter

import android.util.Log
import io.flutter.plugin.common.EventChannel
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.collect
import org.xmtp.android.library.Client
import org.xmtp.android.library.Dm
import org.xmtp.android.library.Group
import com.toii.ai.toii_xmtp_flutter.handlers.ClientHandler

class StreamManager {
    private val activeMessageStreams = mutableMapOf<String, Job>()
    private var conversationStreamJob: Job? = null
    private var currentEventSink: EventChannel.EventSink? = null

    companion object {
        private const val TAG = "XMTP_StreamManager"
        const val STREAM_TYPE_MESSAGES = "messages"
        const val STREAM_TYPE_CONVERSATIONS = "conversations"
    }

    fun setEventSink(eventSink: EventChannel.EventSink?) {
        currentEventSink = eventSink
        Log.d(TAG, "Event sink updated: ${eventSink != null}")
    }

    fun startMessageStream(conversationId: String, scope: CoroutineScope) {
        Log.d(TAG, "Starting message stream for conversation: $conversationId")

        stopMessageStream(conversationId)

        val job = scope.launch {
            try {
                val client = ClientHandler.getClient()
                if (client == null) {
                    sendError("NO_CLIENT", "Client not initialized")
                    return@launch
                }

                val conversation = findConversationWithSync(client, conversationId)
                if (conversation == null) {
                    sendError("CONVERSATION_NOT_FOUND", "Conversation not found: $conversationId")
                    return@launch
                }

                when (conversation) {
                    is Dm -> conversation.sync()
                    is Group -> conversation.sync()
                }

                Log.d(TAG, "Starting XMTP stream for conversation: $conversationId")

                when (conversation) {
                    is Dm -> {
                        conversation.streamMessages().collect { message ->
                            handleMessage(message, conversationId)
                        }
                    }
                    is Group -> {
                        conversation.streamMessages().collect { message ->
                            handleMessage(message, conversationId)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in message stream for $conversationId: ${e.message}", e)
                sendError("STREAM_ERROR", "Message stream error: ${e.message}")
            }
        }

        activeMessageStreams[conversationId] = job
        Log.d(TAG, "Message stream job started for conversation: $conversationId")
    }

    fun stopMessageStream(conversationId: String) {
        activeMessageStreams[conversationId]?.let { job ->
            job.cancel()
            activeMessageStreams.remove(conversationId)
            Log.d(TAG, "Stopped message stream for conversation: $conversationId")
        }
    }

    fun startConversationStream(scope: CoroutineScope) {
        Log.d(TAG, "Starting conversation stream")

        stopConversationStream()

        conversationStreamJob = scope.launch {
            try {
                val client = ClientHandler.getClient()
                if (client == null) {
                    sendError("NO_CLIENT", "Client not initialized")
                    return@launch
                }

                client.conversations.stream().collect { conversation ->
                    handleConversation(conversation)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in conversation stream: ${e.message}", e)
                sendError("STREAM_ERROR", "Conversation stream error: ${e.message}")
            }
        }

        Log.d(TAG, "Conversation stream job started")
    }

    fun stopConversationStream() {
        conversationStreamJob?.let { job ->
            job.cancel()
            conversationStreamJob = null
            Log.d(TAG, "Stopped conversation stream")
        }
    }

    fun stopAllStreams() {
        Log.d(TAG, "Stopping all streams")

        activeMessageStreams.values.forEach { it.cancel() }
        activeMessageStreams.clear()

        stopConversationStream()

        currentEventSink = null

        Log.d(TAG, "All streams stopped")
    }

    private fun handleMessage(message: org.xmtp.android.library.libxmtp.DecodedMessage, conversationId: String) {
        try {
            if (message.id.isNullOrEmpty()) {
                Log.w(TAG, "Skipping message with null/empty ID")
                return
            }

            val messageData = mapOf(
                "streamType" to STREAM_TYPE_MESSAGES,
                "conversationId" to conversationId,
                "id" to message.id,
                "senderInboxId" to (message.senderInboxId ?: ""),
                "content" to (message.body ?: ""),
                "contentType" to (message.encodedContent.type?.typeId ?: "text/plain"),
                "sentAt" to message.sentAt.time,
                "deliveryStatus" to when (message.deliveryStatus) {
                    org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.PUBLISHED -> "published"
                    org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.UNPUBLISHED -> "unpublished"
                    org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.FAILED -> "failed"
                    else -> "published"
                }
            )

            currentEventSink?.success(messageData)
            Log.d(TAG, "Sent message to Flutter: ${message.id} for conversation: $conversationId")
        } catch (e: Exception) {
            Log.e(TAG, "Error handling message: ${e.message}", e)
        }
    }

    private suspend fun handleConversation(conversation: org.xmtp.android.library.Conversation) {
        try {
            val conversationData = when (conversation) {
                is org.xmtp.android.library.Conversation.Dm -> mapOf(
                    "streamType" to STREAM_TYPE_CONVERSATIONS,
                    "type" to "dm",
                    "id" to conversation.dm.id,
                    "peerInboxId" to conversation.dm.peerInboxId,
                    "createdAt" to conversation.dm.createdAt.time
                )
                is org.xmtp.android.library.Conversation.Group -> mapOf(
                    "streamType" to STREAM_TYPE_CONVERSATIONS,
                    "type" to "group",
                    "id" to conversation.group.id,
                    "name" to conversation.group.name,
                    "description" to conversation.group.description,
                    "imageUrl" to conversation.group.imageUrl,
                    "createdAt" to conversation.group.createdAt.time,
                    "memberCount" to conversation.group.members().size
                )
                else -> {
                    Log.w(TAG, "Unknown conversation type: ${conversation::class.simpleName}")
                    return
                }
            }

            currentEventSink?.success(conversationData)
            Log.d(TAG, "Sent conversation to Flutter: ${conversationData["id"]}")
        } catch (e: Exception) {
            Log.e(TAG, "Error handling conversation: ${e.message}", e)
        }
    }

    private fun sendError(code: String, message: String) {
        currentEventSink?.error(code, message, null)
    }

    private suspend fun findConversationWithSync(client: Client, conversationId: String): Any? {
        Log.d(TAG, "Finding conversation with sync: $conversationId")

        var conversation = findConversationInLists(client, conversationId)
        if (conversation != null) {
            Log.d(TAG, "Found conversation in current lists: $conversationId")
            return conversation
        }

        try {
            Log.d(TAG, "Syncing conversations and trying again: $conversationId")
            client.conversations.sync()
            conversation = findConversationInLists(client, conversationId)
            if (conversation != null) {
                Log.d(TAG, "Found conversation after sync: $conversationId")
                return conversation
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during conversation sync: ${e.message}", e)
        }

        Log.w(TAG, "Conversation not found after all attempts: $conversationId")
        return null
    }

    private fun findConversationInLists(client: Client, conversationId: String): Any? {
        client.conversations.listDms().forEach { dm ->
            if (dm.id == conversationId) {
                Log.d(TAG, "Found DM: $conversationId")
                return dm
            }
        }

        client.conversations.listGroups().forEach { group ->
            if (group.id == conversationId) {
                Log.d(TAG, "Found Group: $conversationId")
                return group
            }
        }

        return null
    }
}
