group = "com.toii.ai.toii_xmtp_flutter"
version = "1.0-SNAPSHOT"

buildscript {
    ext.kotlin_version = "1.8.22"
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath("com.android.tools.build:gradle:8.7.0")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: "com.android.library"
apply plugin: "kotlin-android"

android {
    namespace = "com.toii.ai.toii_xmtp_flutter"

    compileSdk = 35

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    sourceSets {
        main.java.srcDirs += "src/main/kotlin"
        test.java.srcDirs += "src/test/kotlin"
    }

    defaultConfig {
        minSdk = 23 // Required for XMTP
    }

    dependencies {
        // XMTP Android Library - using published version
        implementation 'org.xmtp:android:4.4.0'

        // Required dependencies for XMTP
        implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0'
        implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.0'
        implementation 'com.google.crypto.tink:tink-android:1.8.0'
        implementation 'io.grpc:grpc-kotlin-stub:1.4.1'
        implementation 'io.grpc:grpc-okhttp:1.62.2'
        implementation 'io.grpc:grpc-protobuf-lite:1.62.2'
        implementation 'org.web3j:crypto:4.9.4'
        implementation "net.java.dev.jna:jna:5.17.0@aar"
        api 'com.google.protobuf:protobuf-kotlin-lite:3.22.3'
        api 'org.xmtp:proto-kotlin:3.72.4'

        testImplementation("org.jetbrains.kotlin:kotlin-test")
        testImplementation("org.mockito:mockito-core:5.0.0")
    }

    testOptions {
        unitTests.all {
            useJUnitPlatform()

            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
