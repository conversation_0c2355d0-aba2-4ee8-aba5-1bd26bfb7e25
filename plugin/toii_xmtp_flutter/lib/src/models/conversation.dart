/// Base conversation interface
abstract class Conversation {
  String get id;
  String get topic;
  DateTime get createdAt;
  ConversationType get type;
  ConsentState get consentState;
  bool get isActive;

  Map<String, dynamic> toMap();
}

/// Conversation types
enum ConversationType {
  dm,
  group;

  String get value {
    switch (this) {
      case ConversationType.dm:
        return 'dm';
      case ConversationType.group:
        return 'group';
    }
  }

  static ConversationType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'dm':
        return ConversationType.dm;
      case 'group':
        return ConversationType.group;
      default:
        throw ArgumentError('Invalid conversation type: $value');
    }
  }
}

/// Consent state for conversations
enum ConsentState {
  allowed,
  denied,
  unknown;

  String get value {
    switch (this) {
      case ConsentState.allowed:
        return 'allowed';
      case ConsentState.denied:
        return 'denied';
      case ConsentState.unknown:
        return 'unknown';
    }
  }

  static ConsentState fromString(String value) {
    switch (value.toLowerCase()) {
      case 'allowed':
        return ConsentState.allowed;
      case 'denied':
        return ConsentState.denied;
      case 'unknown':
        return ConsentState.unknown;
      default:
        throw ArgumentError('Invalid consent state: $value');
    }
  }
}

/// Direct Message conversation
class Dm implements Conversation {
  @override
  final String id;
  
  @override
  final String topic;
  
  @override
  final DateTime createdAt;
  
  @override
  final ConsentState consentState;
  
  @override
  final bool isActive;
  
  final String peerInboxId;
  final String? peerAddress;

  const Dm({
    required this.id,
    required this.topic,
    required this.createdAt,
    required this.consentState,
    required this.isActive,
    required this.peerInboxId,
    this.peerAddress,
  });

  @override
  ConversationType get type => ConversationType.dm;

  factory Dm.fromMap(Map<String, dynamic> map) {
    return Dm(
      id: map['id'] as String,
      topic: map['topic'] as String,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] as int),
      consentState: ConsentState.fromString(map['consentState'] as String),
      isActive: map['isActive'] as bool,
      peerInboxId: map['peerInboxId'] as String,
      peerAddress: map['peerAddress'] as String?,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'topic': topic,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'consentState': consentState.value,
      'isActive': isActive,
      'peerInboxId': peerInboxId,
      'peerAddress': peerAddress,
      'type': type.value,
    };
  }

  @override
  String toString() {
    return 'Dm(id: $id, peerInboxId: $peerInboxId, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Dm && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
