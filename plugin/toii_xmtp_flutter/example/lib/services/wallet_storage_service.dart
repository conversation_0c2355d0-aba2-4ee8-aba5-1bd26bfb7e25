import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/saved_wallet.dart';

/// Service for managing saved wallet data
class WalletStorageService {
  static const String _savedWalletsKey = 'saved_wallets';
  static const String _lastUsedWalletKey = 'last_used_wallet';

  /// Get all saved wallets
  static Future<List<SavedWallet>> getSavedWallets() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final walletsJson = prefs.getStringList(_savedWalletsKey) ?? [];
      
      return walletsJson
          .map((walletStr) => SavedWallet.fromJson(jsonDecode(walletStr)))
          .toList();
    } catch (e) {
      print('Error loading saved wallets: $e');
      return [];
    }
  }

  /// Save a new wallet
  static Future<bool> saveWallet(SavedWallet wallet) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedWallets = await getSavedWallets();
      
      // Check if wallet with same name already exists
      final existingIndex = savedWallets.indexWhere((w) => w.name == wallet.name);
      if (existingIndex != -1) {
        // Update existing wallet
        savedWallets[existingIndex] = wallet;
      } else {
        // Add new wallet
        savedWallets.add(wallet);
      }
      
      // Convert to JSON strings
      final walletsJson = savedWallets
          .map((wallet) => jsonEncode(wallet.toJson()))
          .toList();
      
      return await prefs.setStringList(_savedWalletsKey, walletsJson);
    } catch (e) {
      print('Error saving wallet: $e');
      return false;
    }
  }

  /// Delete a saved wallet
  static Future<bool> deleteWallet(String walletName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedWallets = await getSavedWallets();
      
      // Remove wallet with matching name
      savedWallets.removeWhere((wallet) => wallet.name == walletName);
      
      // Convert to JSON strings
      final walletsJson = savedWallets
          .map((wallet) => jsonEncode(wallet.toJson()))
          .toList();
      
      return await prefs.setStringList(_savedWalletsKey, walletsJson);
    } catch (e) {
      print('Error deleting wallet: $e');
      return false;
    }
  }

  /// Update wallet with inbox ID after successful connection
  static Future<bool> updateWalletInboxId(String walletName, String inboxId) async {
    try {
      final savedWallets = await getSavedWallets();
      final walletIndex = savedWallets.indexWhere((w) => w.name == walletName);
      
      if (walletIndex == -1) return false;
      
      final updatedWallet = savedWallets[walletIndex].copyWith(
        inboxId: inboxId,
        lastUsed: DateTime.now(),
      );
      
      savedWallets[walletIndex] = updatedWallet;
      
      // Save updated list
      final prefs = await SharedPreferences.getInstance();
      final walletsJson = savedWallets
          .map((wallet) => jsonEncode(wallet.toJson()))
          .toList();
      
      return await prefs.setStringList(_savedWalletsKey, walletsJson);
    } catch (e) {
      print('Error updating wallet inbox ID: $e');
      return false;
    }
  }

  /// Update last used timestamp for a wallet
  static Future<bool> updateLastUsed(String walletName) async {
    try {
      final savedWallets = await getSavedWallets();
      final walletIndex = savedWallets.indexWhere((w) => w.name == walletName);
      
      if (walletIndex == -1) return false;
      
      final updatedWallet = savedWallets[walletIndex].copyWith(
        lastUsed: DateTime.now(),
      );
      
      savedWallets[walletIndex] = updatedWallet;
      
      // Save updated list
      final prefs = await SharedPreferences.getInstance();
      final walletsJson = savedWallets
          .map((wallet) => jsonEncode(wallet.toJson()))
          .toList();
      
      return await prefs.setStringList(_savedWalletsKey, walletsJson);
    } catch (e) {
      print('Error updating last used: $e');
      return false;
    }
  }

  /// Get the last used wallet name
  static Future<String?> getLastUsedWallet() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_lastUsedWalletKey);
    } catch (e) {
      print('Error getting last used wallet: $e');
      return null;
    }
  }

  /// Set the last used wallet name
  static Future<bool> setLastUsedWallet(String walletName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_lastUsedWalletKey, walletName);
    } catch (e) {
      print('Error setting last used wallet: $e');
      return false;
    }
  }

  /// Clear all saved wallets (for testing/reset)
  static Future<bool> clearAllWallets() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_savedWalletsKey);
      await prefs.remove(_lastUsedWalletKey);
      return true;
    } catch (e) {
      print('Error clearing wallets: $e');
      return false;
    }
  }
}
