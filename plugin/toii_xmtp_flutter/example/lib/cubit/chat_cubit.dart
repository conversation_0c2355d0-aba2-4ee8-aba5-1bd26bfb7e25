import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart'
    hide MessageDeliveryStatus;
import 'package:uuid/uuid.dart';

import '../models/chat_models.dart';
import '../utils/conversation_diagnostics.dart';
import 'chat_state.dart';

class ChatCubit extends Cubit<ChatState> {
  final ToiiXmtpFlutter _xmtpPlugin;
  final String _currentUserInboxId;
  StreamSubscription<XmtpMessage>? _messageStreamSubscription;
  Timer? _refreshTimer;
  final List<ChatMessage> _messageCache = [];

  ChatCubit({
    required ToiiXmtpFlutter xmtpPlugin,
    required String currentUserInboxId,
  }) : _xmtpPlugin = xmtpPlugin,
       _currentUserInboxId = currentUserInboxId,
       super(const ChatInitial());

  /// Load chat conversation and messages
  Future<void> loadChat(ChatConversation conversation) async {
    try {
      emit(const ChatLoading());

      // Sync conversation first to ensure we have latest data
      await _syncConversation(conversation.id);

      // Load message history
      final messages = await _loadMessages(conversation.id);

      emit(ChatLoaded(conversation: conversation, messages: messages));

      // Start message streaming
      await _startMessageStreaming(conversation.id);

      // Start periodic refresh as backup
      _startPeriodicRefresh(conversation.id);
    } catch (e) {
      emit(
        ChatError(
          message: 'Failed to load chat: $e',
          conversationId: conversation.id,
        ),
      );
    }
  }

  /// Sync conversation to ensure latest data
  Future<void> _syncConversation(String conversationId) async {
    try {
      // Validate conversation ID format
      if (!_isValidConversationId(conversationId)) {
        debugPrint('Warning: Invalid conversation ID format: $conversationId');
      }

      // Sync conversations to get latest data from network
      await _xmtpPlugin.syncConversations();

      // For groups, also sync the specific group
      // We try to sync as group and ignore errors for DMs since DMs are synced
      // through the general conversation sync above
      try {
        await _xmtpPlugin.syncGroup(conversationId);
      } catch (e) {
        // This is expected for DMs - they don't have individual group sync
        // DMs are synced through the general conversations.sync() call above
      }
    } catch (e) {
      // Log but don't throw - sync failures shouldn't prevent chat loading
      debugPrint('Warning: Failed to sync conversation $conversationId: $e');
    }
  }

  /// Validate conversation ID format
  bool _isValidConversationId(String conversationId) {
    // XMTP conversation IDs are typically hex strings
    // Check if it's a valid hex string of reasonable length
    if (conversationId.isEmpty) return false;

    // Should be hex characters only and reasonable length (typically 32-64 chars)
    final hexRegex = RegExp(r'^[a-fA-F0-9]+$');
    return hexRegex.hasMatch(conversationId) &&
        conversationId.length >= 8 &&
        conversationId.length <= 128;
  }

  /// Load messages for a conversation
  Future<List<ChatMessage>> _loadMessages(String conversationId) async {
    try {
      debugPrint('Loading messages for conversation: $conversationId');
      final xmtpMessages = await _xmtpPlugin.getMessages(conversationId);
      debugPrint('Retrieved ${xmtpMessages.length} XMTP messages');

      final chatMessages = <ChatMessage>[];
      for (final msg in xmtpMessages) {
        try {
          // Validate message before conversion
          if (msg.id.isEmpty) {
            debugPrint('Skipping message with empty ID');
            continue;
          }

          final chatMessage = ChatMessage.fromXmtpMessage(
            msg,
            _currentUserInboxId,
          );
          chatMessages.add(chatMessage);
        } catch (e) {
          debugPrint('Error converting message ${msg.id}: $e');
          // Skip invalid messages and continue processing
        }
      }

      // Sort by sent time (newest first for chat UI)
      chatMessages.sort((a, b) => b.sentAt.compareTo(a.sentAt));

      // Update cache
      _messageCache.clear();
      _messageCache.addAll(chatMessages);

      debugPrint('Successfully loaded ${chatMessages.length} chat messages');
      return chatMessages;
    } catch (e) {
      debugPrint('Error loading messages: $e');
      return [];
    }
  }

  /// Send a message
  Future<void> sendMessage(String content) async {
    final currentState = state;
    if (currentState is! ChatLoaded) return;

    try {
      // Sync conversation before sending to ensure we have latest state
      await _syncConversation(currentState.conversation.id);

      // Create pending message
      final pendingMessageId = const Uuid().v4();
      final pendingMessage = ChatMessage(
        id: pendingMessageId,
        conversationId: currentState.conversation.id,
        senderInboxId: _currentUserInboxId,
        content: content,
        sentAt: DateTime.now(),
        deliveryStatus: MessageDeliveryStatus.pending,
        isFromCurrentUser: true,
      );

      // Add pending message to UI
      final updatedMessages = [pendingMessage, ...currentState.messages];
      emit(
        ChatSendingMessage(
          conversation: currentState.conversation,
          messages: updatedMessages,
          pendingMessageId: pendingMessageId,
          isStreaming: currentState.isStreaming,
        ),
      );

      // Send message via XMTP
      final sentMessageId = await _xmtpPlugin.sendMessage(
        conversationId: currentState.conversation.id,
        content: content,
      );

      // Update message with sent status
      final sentMessage = pendingMessage.copyWith(
        id: sentMessageId,
        deliveryStatus: MessageDeliveryStatus.sent,
      );

      final finalMessages =
          updatedMessages.where((msg) => msg.id != pendingMessageId).toList();
      finalMessages.insert(0, sentMessage);

      emit(
        ChatMessageSent(
          conversation: currentState.conversation,
          messages: finalMessages,
          sentMessageId: sentMessageId,
          isStreaming: currentState.isStreaming,
        ),
      );
    } catch (e) {
      // Handle specific conversation not found error
      if (e.toString().contains('CONVERSATION_NOT_FOUND')) {
        debugPrint(
          'Conversation not found, running diagnostics: ${currentState.conversation.id}',
        );

        // Run diagnostics to understand the issue
        try {
          final diagnostics =
              await ConversationDiagnostics.diagnoseConversation(
                xmtpPlugin: _xmtpPlugin,
                conversationId: currentState.conversation.id,
              );

          debugPrint('Diagnostic results:\n$diagnostics');

          // Try to recover based on diagnostic results
          if (diagnostics.foundAfterSync || !diagnostics.foundInList) {
            debugPrint('Attempting recovery by syncing and retrying...');

            await _xmtpPlugin.syncConversations();
            await Future.delayed(
              const Duration(milliseconds: 500),
            ); // Brief delay for sync

            // Retry sending the message
            final retryMessageId = await _xmtpPlugin.sendMessage(
              conversationId: currentState.conversation.id,
              content: content,
            );

            // If retry succeeds, update the UI
            final currentStateAfterRetry = state;
            if (currentStateAfterRetry is ChatSendingMessage) {
              final sentMessage = currentStateAfterRetry.messages
                  .firstWhere(
                    (msg) => msg.id == currentStateAfterRetry.pendingMessageId,
                  )
                  .copyWith(
                    id: retryMessageId,
                    deliveryStatus: MessageDeliveryStatus.sent,
                  );

              final finalMessages =
                  currentStateAfterRetry.messages
                      .where(
                        (msg) =>
                            msg.id != currentStateAfterRetry.pendingMessageId,
                      )
                      .toList();
              finalMessages.insert(0, sentMessage);

              emit(
                ChatMessageSent(
                  conversation: currentStateAfterRetry.conversation,
                  messages: finalMessages,
                  sentMessageId: retryMessageId,
                  isStreaming: currentStateAfterRetry.isStreaming,
                ),
              );
              return; // Success after retry
            }
          }
        } catch (retryError) {
          debugPrint('Retry failed: $retryError');
          // Fall through to normal error handling
        }
      }

      // Handle send error - mark message as failed
      final currentStateAfterError = state;
      if (currentStateAfterError is ChatSendingMessage) {
        final failedMessages =
            currentStateAfterError.messages.map((msg) {
              if (msg.id == currentStateAfterError.pendingMessageId) {
                return msg.copyWith(
                  deliveryStatus: MessageDeliveryStatus.failed,
                );
              }
              return msg;
            }).toList();

        emit(
          ChatLoaded(
            conversation: currentStateAfterError.conversation,
            messages: failedMessages,
            isStreaming: currentStateAfterError.isStreaming,
          ),
        );
      }

      // Provide more specific error messages
      String errorMessage;
      if (e.toString().contains('CONVERSATION_NOT_FOUND')) {
        errorMessage =
            'Conversation not found. Please try refreshing the conversation list.';
      } else if (e.toString().contains('NO_CLIENT')) {
        errorMessage = 'Client not initialized. Please restart the app.';
      } else {
        errorMessage = 'Failed to send message: $e';
      }

      emit(
        ChatError(
          message: errorMessage,
          conversationId: currentState.conversation.id,
        ),
      );
    }
  }

  /// Start message streaming
  Future<void> _startMessageStreaming(String conversationId) async {
    try {
      await _stopMessageStreaming();

      // Ensure conversation is synced before starting stream
      await _syncConversation(conversationId);

      final messageStream = _xmtpPlugin.streamMessages(conversationId);
      _messageStreamSubscription = messageStream.listen(
        (xmtpMessage) => _handleNewMessage(xmtpMessage),
        onError: (error) {
          print('Message streaming error: $error');
          // Try to restart streaming after a delay on error
          Future.delayed(const Duration(seconds: 5), () {
            if (!isClosed) {
              _startMessageStreaming(conversationId);
            }
          });
        },
        cancelOnError:
            false, // Don't cancel stream on individual message errors
      );

      final currentState = state;
      if (currentState is ChatLoaded) {
        emit(
          ChatStreamingStarted(
            conversation: currentState.conversation,
            messages: currentState.messages,
          ),
        );
      }
    } catch (e) {
      print('Failed to start message streaming: $e');
    }
  }

  /// Handle new message from stream
  void _handleNewMessage(XmtpMessage xmtpMessage) {
    try {
      final currentState = state;
      if (currentState is! ChatLoaded) return;

      // Validate message data
      if (xmtpMessage.id.isEmpty) {
        debugPrint('Skipping message with empty ID');
        return;
      }

      // Skip if message is from current user (already handled by send)
      if (xmtpMessage.senderInboxId == _currentUserInboxId) {
        debugPrint('Skipping own message: ${xmtpMessage.id}');
        return;
      }

      // Check if message already exists
      final existingMessage = currentState.messages.any(
        (msg) => msg.id == xmtpMessage.id,
      );
      if (existingMessage) {
        debugPrint('Message already exists: ${xmtpMessage.id}');
        return;
      }

      debugPrint(
        'Processing new message: ${xmtpMessage.id} from ${xmtpMessage.senderInboxId}',
      );

      final newMessage = ChatMessage.fromXmtpMessage(
        xmtpMessage,
        _currentUserInboxId,
      );

      // Insert new message at the beginning (newest first)
      final updatedMessages = [newMessage, ...currentState.messages];

      emit(
        ChatMessageReceived(
          conversation: currentState.conversation,
          messages: updatedMessages,
          newMessage: newMessage,
          isStreaming: currentState.isStreaming,
        ),
      );

      debugPrint('New message added to chat: ${newMessage.content}');
    } catch (e) {
      debugPrint('Error handling new message: $e');
      // Don't emit error state for individual message failures
      // Just log the error and continue
    }
  }

  /// Start periodic refresh as backup for streaming
  void _startPeriodicRefresh(String conversationId) {
    _stopPeriodicRefresh();

    // Refresh every 30 seconds as backup
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      if (isClosed) {
        timer.cancel();
        return;
      }

      final currentState = state;
      if (currentState is ChatLoaded &&
          currentState.conversation.id == conversationId) {
        try {
          debugPrint('Periodic refresh: checking for new messages');
          final latestMessages = await _loadMessages(conversationId);

          // Only update if we have new messages
          if (latestMessages.length > currentState.messages.length) {
            debugPrint(
              'Found ${latestMessages.length - currentState.messages.length} new messages via refresh',
            );
            emit(
              ChatLoaded(
                conversation: currentState.conversation,
                messages: latestMessages,
                isStreaming: currentState.isStreaming,
              ),
            );
          }
        } catch (e) {
          debugPrint('Error during periodic refresh: $e');
        }
      }
    });
  }

  /// Stop periodic refresh
  void _stopPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Stop message streaming
  Future<void> _stopMessageStreaming() async {
    await _messageStreamSubscription?.cancel();
    _messageStreamSubscription = null;

    final currentState = state;
    if (currentState is ChatLoaded && currentState.isStreaming) {
      emit(
        ChatStreamingStopped(
          conversation: currentState.conversation,
          messages: currentState.messages,
        ),
      );
    }
  }

  /// Refresh messages
  Future<void> refreshMessages() async {
    final currentState = state;
    if (currentState is! ChatLoaded) return;

    try {
      // Sync conversation before refreshing messages
      await _syncConversation(currentState.conversation.id);

      final messages = await _loadMessages(currentState.conversation.id);
      emit(currentState.copyWith(messages: messages));
    } catch (e) {
      emit(
        ChatError(
          message: 'Failed to refresh messages: $e',
          conversationId: currentState.conversation.id,
        ),
      );
    }
  }

  @override
  Future<void> close() async {
    await _stopMessageStreaming();
    _stopPeriodicRefresh();
    return super.close();
  }
}

/// Extension to add copyWith method to ChatMessage
extension ChatMessageCopyWith on ChatMessage {
  ChatMessage copyWith({
    String? id,
    String? conversationId,
    String? senderInboxId,
    String? content,
    DateTime? sentAt,
    MessageDeliveryStatus? deliveryStatus,
    bool? isFromCurrentUser,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      conversationId: conversationId ?? this.conversationId,
      senderInboxId: senderInboxId ?? this.senderInboxId,
      content: content ?? this.content,
      sentAt: sentAt ?? this.sentAt,
      deliveryStatus: deliveryStatus ?? this.deliveryStatus,
      isFromCurrentUser: isFromCurrentUser ?? this.isFromCurrentUser,
    );
  }
}
