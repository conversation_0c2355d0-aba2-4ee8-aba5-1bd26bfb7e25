import 'package:equatable/equatable.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart' as xmtp;

/// Chat message model that extends flutter_chat_ui types.Message
class ChatMessage extends Equatable {
  final String id;
  final String conversationId;
  final String senderInboxId;
  final String content;
  final DateTime sentAt;
  final MessageDeliveryStatus deliveryStatus;
  final bool isFromCurrentUser;

  const ChatMessage({
    required this.id,
    required this.conversationId,
    required this.senderInboxId,
    required this.content,
    required this.sentAt,
    required this.deliveryStatus,
    required this.isFromCurrentUser,
  });

  /// Convert to flutter_chat_ui types.Message
  types.Message toFlutterChatMessage() {
    // Safe substring for senderInboxId
    String displayName;
    if (isFromCurrentUser) {
      displayName = 'You';
    } else {
      displayName =
          senderInboxId.length >= 8
              ? senderInboxId.substring(0, 8)
              : senderInboxId;
    }

    return types.TextMessage(
      author: types.User(id: senderInboxId, firstName: displayName),
      createdAt: sentAt.millisecondsSinceEpoch,
      id: id,
      text: content,
      status: _mapDeliveryStatus(deliveryStatus),
    );
  }

  types.Status? _mapDeliveryStatus(MessageDeliveryStatus status) {
    switch (status) {
      case MessageDeliveryStatus.pending:
        return types.Status.sending;
      case MessageDeliveryStatus.sent:
        return types.Status.sent;
      case MessageDeliveryStatus.delivered:
        return types.Status.delivered;
      case MessageDeliveryStatus.failed:
        return types.Status.error;
    }
  }

  /// Create from XMTP message
  factory ChatMessage.fromXmtpMessage(
    xmtp.XmtpMessage xmtpMessage,
    String currentUserInboxId,
  ) {
    // Map XMTP delivery status to our local enum
    MessageDeliveryStatus localStatus;
    switch (xmtpMessage.deliveryStatus) {
      case xmtp.MessageDeliveryStatus.published:
        localStatus = MessageDeliveryStatus.sent;
        break;
      case xmtp.MessageDeliveryStatus.unpublished:
        localStatus = MessageDeliveryStatus.pending;
        break;
      case xmtp.MessageDeliveryStatus.failed:
        localStatus = MessageDeliveryStatus.failed;
        break;
      default:
        localStatus = MessageDeliveryStatus.sent;
    }

    return ChatMessage(
      id: xmtpMessage.id,
      conversationId: xmtpMessage.conversationId,
      senderInboxId: xmtpMessage.senderInboxId,
      content: xmtpMessage.content,
      sentAt: xmtpMessage.sentAt,
      deliveryStatus: localStatus,
      isFromCurrentUser: xmtpMessage.senderInboxId == currentUserInboxId,
    );
  }

  @override
  List<Object?> get props => [
    id,
    conversationId,
    senderInboxId,
    content,
    sentAt,
    deliveryStatus,
    isFromCurrentUser,
  ];
}

/// Chat conversation model
class ChatConversation extends Equatable {
  final String id;
  final String title;
  final xmtp.ConversationType type;
  final DateTime createdAt;
  final xmtp.ConsentState consentState;
  final bool isActive;
  final String? peerInboxId; // For DMs
  final List<String>? memberInboxIds; // For Groups
  final String? lastMessageContent;
  final DateTime? lastMessageTime;
  final int unreadCount;

  const ChatConversation({
    required this.id,
    required this.title,
    required this.type,
    required this.createdAt,
    required this.consentState,
    required this.isActive,
    this.peerInboxId,
    this.memberInboxIds,
    this.lastMessageContent,
    this.lastMessageTime,
    this.unreadCount = 0,
  });

  /// Create from XMTP Conversation
  factory ChatConversation.fromConversation(xmtp.Conversation conversation) {
    String title;
    String? peerInboxId;
    List<String>? memberInboxIds;

    if (conversation is xmtp.Dm) {
      // Safe substring for peerInboxId
      final peerIdDisplay =
          conversation.peerInboxId.length >= 8
              ? conversation.peerInboxId.substring(0, 8)
              : conversation.peerInboxId;
      title = 'DM with $peerIdDisplay...';
      peerInboxId = conversation.peerInboxId;
    } else {
      // Safe substring for conversation id
      final conversationIdDisplay =
          conversation.id.length >= 8
              ? conversation.id.substring(0, 8)
              : conversation.id;
      title = 'Group: $conversationIdDisplay...';
      // TODO: Get actual member list from group conversation
      memberInboxIds = [];
    }

    return ChatConversation(
      id: conversation.id,
      title: title,
      type: conversation.type,
      createdAt: conversation.createdAt,
      consentState: conversation.consentState,
      isActive: conversation.isActive,
      peerInboxId: peerInboxId,
      memberInboxIds: memberInboxIds,
    );
  }

  ChatConversation copyWith({
    String? id,
    String? title,
    xmtp.ConversationType? type,
    DateTime? createdAt,
    xmtp.ConsentState? consentState,
    bool? isActive,
    String? peerInboxId,
    List<String>? memberInboxIds,
    String? lastMessageContent,
    DateTime? lastMessageTime,
    int? unreadCount,
  }) {
    return ChatConversation(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      consentState: consentState ?? this.consentState,
      isActive: isActive ?? this.isActive,
      peerInboxId: peerInboxId ?? this.peerInboxId,
      memberInboxIds: memberInboxIds ?? this.memberInboxIds,
      lastMessageContent: lastMessageContent ?? this.lastMessageContent,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }

  @override
  List<Object?> get props => [
    id,
    title,
    type,
    createdAt,
    consentState,
    isActive,
    peerInboxId,
    memberInboxIds,
    lastMessageContent,
    lastMessageTime,
    unreadCount,
  ];
}

/// Message delivery status enum
enum MessageDeliveryStatus { pending, sent, delivered, failed }
