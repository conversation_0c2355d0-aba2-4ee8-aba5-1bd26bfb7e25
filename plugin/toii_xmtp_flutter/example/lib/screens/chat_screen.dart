import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

import '../cubit/chat_cubit.dart';
import '../cubit/chat_state.dart' as chat_state;
import '../models/chat_models.dart';

class ChatScreen extends StatelessWidget {
  final ChatConversation conversation;
  final String currentUserInboxId;

  const ChatScreen({
    super.key,
    required this.conversation,
    required this.currentUserInboxId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) => ChatCubit(
            xmtpPlugin: ToiiXmtpFlutter(),
            currentUserInboxId: currentUserInboxId,
          )..loadChat(conversation),
      child: <PERSON><PERSON><PERSON><PERSON><PERSON>(
        conversation: conversation,
        currentUserInboxId: currentUserInboxId,
      ),
    );
  }
}

class ChatView extends StatelessWidget {
  final ChatConversation conversation;
  final String currentUserInboxId;

  const ChatView({
    super.key,
    required this.conversation,
    required this.currentUserInboxId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              conversation.title,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            BlocBuilder<ChatCubit, chat_state.ChatState>(
              builder: (context, state) {
                String subtitle = '';
                if (state is chat_state.ChatLoading) {
                  subtitle = 'Loading...';
                } else if (state is chat_state.ChatLoaded) {
                  if (conversation.type == ConversationType.dm) {
                    subtitle =
                        'DM • ${state.isStreaming ? 'Online' : 'Offline'}';
                  } else {
                    subtitle = 'Group • ${state.messages.length} messages';
                  }
                } else if (state is chat_state.ChatError) {
                  subtitle = 'Error';
                }

                return Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.normal,
                  ),
                );
              },
            ),
          ],
        ),
        actions: [
          BlocBuilder<ChatCubit, chat_state.ChatState>(
            builder: (context, state) {
              if (state is chat_state.ChatLoaded && state.isStreaming) {
                return const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Icon(Icons.circle, color: Colors.green, size: 12),
                );
              }
              return const SizedBox.shrink();
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<ChatCubit>().refreshMessages();
            },
          ),
        ],
      ),
      body: BlocConsumer<ChatCubit, chat_state.ChatState>(
        listener: (context, state) {
          if (state is chat_state.ChatError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
                action: SnackBarAction(
                  label: 'Retry',
                  textColor: Colors.white,
                  onPressed: () {
                    context.read<ChatCubit>().loadChat(conversation);
                  },
                ),
              ),
            );
          } else if (state is chat_state.ChatMessageSent) {
            // Optional: Show success feedback
          } else if (state is chat_state.ChatMessageReceived) {
            // Optional: Show new message notification
          }
        },
        builder: (context, state) {
          if (state is chat_state.ChatLoading) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading messages...'),
                ],
              ),
            );
          }

          if (state is chat_state.ChatError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load chat',
                    style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey[500]),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      context.read<ChatCubit>().loadChat(conversation);
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is chat_state.ChatLoaded) {
            return _buildChatUI(context, state);
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildChatUI(BuildContext context, chat_state.ChatLoaded state) {
    final user = types.User(id: currentUserInboxId, firstName: 'You');

    final messages =
        state.messages
            .map((chatMessage) => chatMessage.toFlutterChatMessage())
            .toList();

    return Chat(
      messages: messages,
      onSendPressed: (types.PartialText message) {
        context.read<ChatCubit>().sendMessage(message.text);
      },
      user: user,
      theme: const DefaultChatTheme(
        primaryColor: Colors.blue,
        secondaryColor: Color(0xFFF5F5F5),
        backgroundColor: Colors.white,
        inputBackgroundColor: Color(0xFFF5F5F5),
        inputTextColor: Colors.black87,
        messageBorderRadius: 16,
        messageInsetsHorizontal: 12,
        messageInsetsVertical: 8,
      ),
      showUserAvatars: conversation.type == ConversationType.group,
      showUserNames: conversation.type == ConversationType.group,
      emptyState: _buildEmptyState(),
      customBottomWidget:
          state is chat_state.ChatSendingMessage
              ? _buildSendingIndicator()
              : null,
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            conversation.type == ConversationType.dm
                ? Icons.person_outline
                : Icons.group_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            conversation.type == ConversationType.dm
                ? 'Start your conversation'
                : 'Welcome to the group',
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Send a message to get started',
            style: TextStyle(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildSendingIndicator() {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 8),
          Text(
            'Sending...',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
        ],
      ),
    );
  }
}
