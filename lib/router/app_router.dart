import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/core/constant/key_shared.dart';
import 'package:toii_mesh/screen/login/create_account_screen.dart';
import 'package:toii_mesh/screen/main_tabbar/main_tabbar_screen.dart';
import 'package:toii_mesh/screen/onboarding/onboarding_screen.dart';
import 'package:toii_mesh/screen/splash/splash_screen.dart';
import 'package:toii_mesh/screen/start/start_screen.dart';
import 'package:toii_mesh/screen/user_profile/user_profile_screen.dart';
import 'package:toii_mesh/utils/navigation_service.dart';
import 'package:toii_mesh/utils/shared_prefs/shared_prefs.dart';

enum RouterEnums {
  splash('/splash'),
  onboarding('/onboarding'),
  inital('/'),
  start('/start'),
  createAcount('/create-account'),
  mainTabbar('/main-tabbar'),
  userProfile('/user-profile');

  final String routeName;

  const RouterEnums(this.routeName);
}

final registerNavigatorKey = GlobalKey<NavigatorState>();
final GoRouter router = GoRouter(
  initialLocation: RouterEnums.splash.routeName,
  navigatorKey: GetIt.instance<NavigationService>().navigatorKey,
  debugLogDiagnostics: true,
  routes: [
    GoRoute(
      path: RouterEnums.start.routeName,
      builder: (context, state) => const StartScreen(),
    ),
    GoRoute(
      path: RouterEnums.splash.routeName,
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: RouterEnums.onboarding.routeName,
      builder: (context, state) => const OnboardingScreen(),
    ),
    GoRoute(
      path: RouterEnums.createAcount.routeName,
      builder: (context, state) => CreateAccountScreen(
        address: state.extra as String?,
      ),
    ),
    GoRoute(
      path: RouterEnums.mainTabbar.routeName,
      builder: (context, state) => const MainTabbarScreen(),
    ),
    GoRoute(
      path: RouterEnums.userProfile.routeName,
      builder: (context, state) {
        final extra = state.extra;
        if (extra is Map<String, dynamic>) {
          return UserProfileScreen(
            user: extra['user'],
            figmaPerson: extra['figmaPerson'],
          );
        }
        return const UserProfileScreen();
      },
    ),
  ],
  redirect: (context, state) async {
    final path = state.uri.path;
    if (path == RouterEnums.inital.routeName) {
      final accessToken = SharedPref.getString(KeyShared.tokenKey);
      final isLogged = SharedPref.getBool(KeyShared.isLogin) ?? false;

      if (accessToken.isNotEmpty && isLogged == true) {
        return RouterEnums.mainTabbar.routeName;
      } else {
        return RouterEnums.start.routeName;
      }
    } else {
      return state.uri.path;
    }
  },
);
