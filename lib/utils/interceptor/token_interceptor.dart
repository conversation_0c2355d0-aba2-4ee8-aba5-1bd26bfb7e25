import 'package:dio/dio.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/core/constant/key_shared.dart';
import 'package:toii_mesh/router/app_router.dart';
import 'package:toii_mesh/utils/device_id/device_id_utils.dart';
import 'package:toii_mesh/utils/navigation_service.dart';
import 'package:toii_mesh/utils/shared_prefs/shared_prefs.dart';
import 'package:toii_mesh/widget/snack_bar/snackbar.dart';
 

class TokenInterceptor extends InterceptorsWrapper {
  @override
  Future onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final accessToken = SharedPref.getString(KeyShared.tokenKey);
    final tmpToken = SharedPref.getString(KeyShared.tmpTokenKey);
    final deviceId = SharedPref.getString(deviceIDKey);
    options.headers["Device-ID"] = deviceId;
    options.headers["Authorization"] = "Bearer $accessToken";
    options.headers["Tmp-Token"] = tmpToken;
    options.headers["App-Type"] = "toii-social";
    return super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    final status = response.statusCode;
    final isValid = status != null && status >= 200 && status < 300;
    if (!isValid) {
      throw DioException.badResponse(
        statusCode: status!,
        requestOptions: response.requestOptions,
        response: response,
      );
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    super.onError(err, handler);
    final statusCode = err.response?.statusCode;
    final requestUri = err.response?.requestOptions.uri.path;

    final whiteListUri = ["auth/login"];

    final isWhiteList = whiteListUri.any(
      (p) => requestUri?.contains(p) ?? false,
    );

    if (statusCode == 401 && !isWhiteList) {
      // GetIt.instance.get<SharedPreferencesManager>().clear();
      // GetIt.instance<DataDeviceCubit>().clear();
      SharedPref.setBool(KeyShared.isLogin, false);
      SharedPref().clearKey(KeyShared.tokenKey);
      getContext.go(RouterEnums.inital.routeName);

      Future.delayed(const Duration(seconds: 2), () {
        getContext.showSnackbar(
          message: err.response?.data["message"] ?? "You have been logged out.",
        );
      });
    } else {
      switch (err.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          throw DeadlineExceededException(err.requestOptions, err.response);
        case DioExceptionType.badResponse:
          if (err.response == null) {
            throw BadRequestException(err.requestOptions, err.response);
          }
          switch (err.response?.statusCode) {
            case 400:
            case 429:
              throw BadRequestException(err.requestOptions, err.response);
            case 401:
              throw UnauthorizedException(err.requestOptions, err.response);
            case 404:
              throw NotFoundException(err.requestOptions, err.response);
            case 409:
              throw ConflictException(err.requestOptions, err.response);
            case 500:
              throw InternalServerErrorException(
                err.requestOptions,
                err.response,
              );
            default:
              throw BadRequestException(err.requestOptions, err.response);
          }

        case DioExceptionType.cancel:
          throw BadRequestException(err.requestOptions, err.response);
        case DioExceptionType.unknown:
          throw NoInternetConnectionException(err.requestOptions, err.response);
        case DioExceptionType.badCertificate:
          throw BadRequestException(err.requestOptions, err.response);
        case DioExceptionType.connectionError:
          throw NoInternetConnectionException(err.requestOptions, err.response);
      }
    }

    return super.onError(err, handler);
  }
}

class BadRequestException extends DioException {
  BadRequestException(RequestOptions r, Response? response)
    : super(requestOptions: r, response: response);

  @override
  String toString() {
    return response?.data['message'] ?? 'Invalid request';
  }
}

class InternalServerErrorException extends DioException {
  InternalServerErrorException(RequestOptions r, Response? response)
    : super(requestOptions: r, response: response);

  @override
  String toString() {
    return response?.data['message'] ??
        'Unknown error occurred, please try again later.';
  }
}

class ConflictException extends DioException {
  ConflictException(RequestOptions r, Response? response)
    : super(requestOptions: r, response: response);

  @override
  String toString() {
    return response?.data['message'] ?? 'Conflict occurred';
  }
}

class UnauthorizedException extends DioException {
  UnauthorizedException(RequestOptions r, Response? response)
    : super(requestOptions: r, response: response);

  @override
  String toString() {
    return response?.data['message'] ?? 'Access denied';
  }
}

class NotFoundException extends DioException {
  NotFoundException(RequestOptions r, Response? response)
    : super(requestOptions: r, response: response);

  @override
  String toString() {
    return response?.data['message'] ??
        'The requested information could not be found';
  }
}

class NoInternetConnectionException extends DioException {
  NoInternetConnectionException(RequestOptions r, Response? response)
    : super(requestOptions: r, response: response);

  @override
  String toString() {
    return response?.data['message'] ??
        'You have no internet connection, please check again.';
  }
}

class DeadlineExceededException extends DioException {
  DeadlineExceededException(RequestOptions r, Response? response)
    : super(requestOptions: r, response: response);

  @override
  String toString() {
    return response?.data['message'] ??
        'The connection has timed out, please try again.';
  }
}
