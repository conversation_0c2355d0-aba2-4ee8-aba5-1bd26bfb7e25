import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:toii_mesh/model/base/base_response.dart';
import 'package:toii_mesh/model/log_api/log_api_singleton.dart';
import 'package:toii_mesh/utils/extensions/map_extensions.dart';
 

class LogAPIInterceptor implements InterceptorsWrapper {
  String getErrorMessage(DioException err) {
    var message = '';

    switch (err.type) {
      case DioExceptionType.cancel:
        message = 'Cancel';
      case DioExceptionType.connectionTimeout:
        message = 'ConnectTimeout';
      case DioExceptionType.receiveTimeout:
        message = 'ReceiveTimeout';
      case DioExceptionType.sendTimeout:
        message = 'SendTimeout';
      case DioExceptionType.badResponse:
        message = (err.response?.data ?? '').toString();
      case DioExceptionType.connectionError:
        message = err.message ?? 'ConnectionError';
      case DioExceptionType.unknown:
        message = err.message ?? 'unknown';
      case DioExceptionType.badCertificate:
        message = err.message ?? 'badCertificate';
    }

    if (message.trim().isEmpty) message = (err.response?.data ?? '').toString();

    if (message.trim().isEmpty) message = err.error?.toString() ?? '';

    return message;
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    addAPILOGFromRequestOptions(
      err.requestOptions,
      statusCode: err.response?.statusCode?.toString() ?? '',
      response: getErrorMessage(err),
      isError: true,
    );

    return handler.next(err);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    try {
      final object = json.decode(response.toString());
      final prettyString = const JsonEncoder.withIndent('  ').convert(object);

      final baseResponse =
          object is Map<String, dynamic>
              ? BaseResponse.fromJson(
                Map<String, dynamic>.from(object),
                (json) => () {}(),
              )
              : null;

      addAPILOGFromRequestOptions(
        response.requestOptions,
        response: prettyString,
        statusCode: response.statusCode.toString(),
        //   isError: baseResponse?.isSuccess() == false,
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }

    return handler.next(response);
  }

  Future<void> addAPILOGFromRequestOptions(
    RequestOptions requestOptions, {
    String statusCode = '',
    String response = '',
    bool isError = false,
  }) async {
    try {
      final singleTon = LogAPISingleTon();
      final cUrl = getCurl(requestOptions);
      var prettyData = '';

      const jsonEncoder = JsonEncoder.withIndent('  ');

      if (requestOptions.method.toUpperCase() == 'GET') {
        prettyData = jsonEncoder.convert(
          requestOptions.queryParameters.removeSensitiveData(),
        );
      } else {
        prettyData =
            requestOptions.data is Map
                ? jsonEncoder.convert(requestOptions.data as Map)
                : '';
      }

      final headers = jsonEncoder.convert(requestOptions.headers);

      final model = LogAPIModel(
        uri: requestOptions.uri.toString(),
        headers: headers,
        method: requestOptions.method,
        body: prettyData,
        status: statusCode,
        response: response,
        isError: isError,
        cURL: cUrl,
      );

      singleTon.data.insert(0, model);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    return handler.next(options);
  }

  String getCurl(RequestOptions options) {
    var value = '';
    try {
      final qp = options.queryParameters;
      final h = options.headers;
      final d = json.encode(options.data);

      final curl =
          'curl -X ${options.method} \'${options.baseUrl}${options.path}' +
          (qp.length != 0
              ? qp.keys.fold(
                '',
                (value, key) =>
                    '$value${value.length == 0 ? '?' : '&'}$key=${qp[key]}',
              )
              : '') +
          '\'' +
          h.keys.fold('', (value, key) => '$value -H \'$key: ${h[key]}\'') +
          (d.length != 0 ? ' --data-binary \'$d\'' : '') +
          ' --insecure';

      // print('server_curl: $curl');
      value = curl;
    } catch (e) {
      // print('CurlInterceptor error: $e');
    }
    return value;
  }
}
