// import 'package:web3dart/web3dart.dart';

// class EthAmountFormatter {
//   EthAmountFormatter(this.amount);

//   final BigInt? amount;

//   String format({
//     EtherUnit fromUnit = EtherUnit.wei,
//     EtherUnit toUnit = EtherUnit.ether,
//   }) {
//     try {
//       if (amount == null) {
//         return '-';
//       }

//       return EtherAmount.fromBigInt(fromUnit, amount!)
//           .getValueInUnit(toUnit)
//           .toString();
//     } catch (e) {
//       return "0";
//     }
//   }
// }
