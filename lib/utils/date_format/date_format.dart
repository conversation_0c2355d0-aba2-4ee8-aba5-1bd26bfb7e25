// import 'package:intl/intl.dart';

// extension on DateTime {
//   bool isSameDay(DateTime dateTime) {
//     return day == dateTime.day &&
//         month == dateTime.month &&
//         year == dateTime.year;
//   }
// }

// String formatDate(DateTime date) {
//   if (date.isSameDay(DateTime.now())) {
//     return DateFormat('hh:mm a').format(date).toString();
//   }
//   return DateFormat('dd/MM/yyyy hh:mm a').format(date).toString();
// }
