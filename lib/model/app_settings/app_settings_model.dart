import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'app_settings_model.g.dart';

@JsonSerializable()
class AppSettingsDataModel extends Equatable {
  final String id;
  final AppVersionModel? version;
  final AppConfigModel? config;
  @Json<PERSON>ey(name: 'is_active')
  final bool? isActive;
  @Json<PERSON>ey(name: 'created_at')
  final String? createdAt;
  @J<PERSON><PERSON>ey(name: 'updated_at')
  final String? updatedAt;

  const AppSettingsDataModel({
    required this.id,
    this.version,
    this.config,
    this.isActive,
    this.createdAt,
    this.updatedAt,
  });

  factory AppSettingsDataModel.fromJson(Map<String, dynamic> json) =>
      _$AppSettingsDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppSettingsDataModelToJson(this);

  @override
  List<Object?> get props => [
    id,
    version,
    config,
    isActive,
    createdAt,
    updatedAt,
  ];
}

@JsonSerializable()
class AppVersionModel extends Equatable {
  final int? major;
  final int? minor;
  final int? patch;
  final String? build;
  final String? version;
  final String? platform;
  @JsonKey(name: 'min_os_version')
  final String? minOsVersion;
  @JsonKey(name: 'max_os_version')
  final String? maxOsVersion;

  const AppVersionModel({
    this.major,
    this.minor,
    this.patch,
    this.build,
    this.version,
    this.platform,
    this.minOsVersion,
    this.maxOsVersion,
  });

  factory AppVersionModel.fromJson(Map<String, dynamic> json) =>
      _$AppVersionModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppVersionModelToJson(this);

  @override
  List<Object?> get props => [
    major,
    minor,
    patch,
    build,
    version,
    platform,
    minOsVersion,
    maxOsVersion,
  ];
}

@JsonSerializable()
class AppConfigModel extends Equatable {
  final bool? forceUpdate;
  final bool? onLoginGuest;

  const AppConfigModel({this.forceUpdate, this.onLoginGuest});

  factory AppConfigModel.fromJson(Map<String, dynamic> json) =>
      _$AppConfigModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppConfigModelToJson(this);

  @override
  List<Object?> get props => [forceUpdate, onLoginGuest];
}
