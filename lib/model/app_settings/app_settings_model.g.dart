// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_settings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppSettingsDataModel _$AppSettingsDataModelFromJson(
        Map<String, dynamic> json) =>
    AppSettingsDataModel(
      id: json['id'] as String,
      version: json['version'] == null
          ? null
          : AppVersionModel.fromJson(json['version'] as Map<String, dynamic>),
      config: json['config'] == null
          ? null
          : AppConfigModel.fromJson(json['config'] as Map<String, dynamic>),
      isActive: json['is_active'] as bool?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$AppSettingsDataModelToJson(
        AppSettingsDataModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'version': instance.version,
      'config': instance.config,
      'is_active': instance.isActive,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

AppVersionModel _$AppVersionModelFromJson(Map<String, dynamic> json) =>
    AppVersionModel(
      major: (json['major'] as num?)?.toInt(),
      minor: (json['minor'] as num?)?.toInt(),
      patch: (json['patch'] as num?)?.toInt(),
      build: json['build'] as String?,
      version: json['version'] as String?,
      platform: json['platform'] as String?,
      minOsVersion: json['min_os_version'] as String?,
      maxOsVersion: json['max_os_version'] as String?,
    );

Map<String, dynamic> _$AppVersionModelToJson(AppVersionModel instance) =>
    <String, dynamic>{
      'major': instance.major,
      'minor': instance.minor,
      'patch': instance.patch,
      'build': instance.build,
      'version': instance.version,
      'platform': instance.platform,
      'min_os_version': instance.minOsVersion,
      'max_os_version': instance.maxOsVersion,
    };

AppConfigModel _$AppConfigModelFromJson(Map<String, dynamic> json) =>
    AppConfigModel(
      forceUpdate: json['forceUpdate'] as bool?,
      onLoginGuest: json['onLoginGuest'] as bool?,
    );

Map<String, dynamic> _$AppConfigModelToJson(AppConfigModel instance) =>
    <String, dynamic>{
      'forceUpdate': instance.forceUpdate,
      'onLoginGuest': instance.onLoginGuest,
    };
