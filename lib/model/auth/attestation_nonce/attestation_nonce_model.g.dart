// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attestation_nonce_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttestationNonceModel _$AttestationNonceModelFromJson(
        Map<String, dynamic> json) =>
    AttestationNonceModel(
      nonce: json['nonce'] as String,
    );

Map<String, dynamic> _$AttestationNonceModelToJson(
        AttestationNonceModel instance) =>
    <String, dynamic>{
      'nonce': instance.nonce,
    };

AttestationRegisterRequestModel _$AttestationRegisterRequestModelFromJson(
        Map<String, dynamic> json) =>
    AttestationRegisterRequestModel(
      attestationData: json['attestationData'] as String,
      keyId: json['keyId'] as String,
      networkBase: json['networkBase'] as String,
      walletAddress: json['walletAddress'] as String,
      walletProvider: json['walletProvider'] as String,
    );

Map<String, dynamic> _$AttestationRegisterRequestModelToJson(
        AttestationRegisterRequestModel instance) =>
    <String, dynamic>{
      'attestationData': instance.attestationData,
      'keyId': instance.keyId,
      'networkBase': instance.networkBase,
      'walletAddress': instance.walletAddress,
      'walletProvider': instance.walletProvider,
    };
