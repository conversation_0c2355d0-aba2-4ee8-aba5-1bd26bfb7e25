import 'package:json_annotation/json_annotation.dart';

part 'attestation_nonce_model.g.dart';

@JsonSerializable()
class AttestationNonceModel {
  const AttestationNonceModel({required this.nonce});

  final String nonce;
  factory AttestationNonceModel.fromJson(Map<String, dynamic> json) =>
      _$AttestationNonceModelFromJson(json);

  Map<String, dynamic> toJson() => _$AttestationNonceModelToJson(this);
}

@JsonSerializable()
class AttestationRegisterRequestModel {
  const AttestationRegisterRequestModel({
    required this.attestationData,
    required this.keyId,
    required this.networkBase,
    required this.walletAddress,
    required this.walletProvider,
  });
  final String attestationData;
  final String keyId;
  final String networkBase;
  final String walletAddress;
  final String walletProvider;

  factory AttestationRegisterRequestModel.fromJson(Map<String, dynamic> json) =>
      _$AttestationRegisterRequestModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$AttestationRegisterRequestModelToJson(this);
}
