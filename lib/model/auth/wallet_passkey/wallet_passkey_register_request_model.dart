import 'package:json_annotation/json_annotation.dart';

import 'webauthn_response_model.dart';

part 'wallet_passkey_register_request_model.g.dart';

@JsonSerializable()
class WalletPasskeyRegisterRequestModel {
  const WalletPasskeyRegisterRequestModel({
    this.address,
    this.signature,
    this.attestationInfo,
    this.assertionInfo,
    this.userId,
  });

  final String? address;
  final String? signature;
  final WebAuthnResponseModel? attestationInfo;
  final WebAuthnResponseModel? assertionInfo;
  final String? userId;

  factory WalletPasskeyRegisterRequestModel.fromJson(
    Map<String, dynamic> json,
  ) => _$WalletPasskeyRegisterRequestModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$WalletPasskeyRegisterRequestModelToJson(this);
}
