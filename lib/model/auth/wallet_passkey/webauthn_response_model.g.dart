// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'webauthn_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WebAuthnResponseModel _$WebAuthnResponseModelFromJson(
        Map<String, dynamic> json) =>
    WebAuthnResponseModel(
      id: json['id'] as String,
      rawId: json['rawId'] as String,
      response: WebAuthnAttestationResponseModel.fromJson(
          json['response'] as Map<String, dynamic>),
      type: json['type'] as String,
    );

Map<String, dynamic> _$WebAuthnResponseModelToJson(
        WebAuthnResponseModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'rawId': instance.rawId,
      'response': instance.response,
      'type': instance.type,
    };

WebAuthnAttestationResponseModel _$WebAuthnAttestationResponseModelFromJson(
        Map<String, dynamic> json) =>
    WebAuthnAttestationResponseModel(
      attestationObject: json['attestationObject'] as String?,
      clientDataJSON: json['clientDataJSON'] as String?,
      signature: json['signature'] as String?,
      userHandle: json['userHandle'] as String?,
      authenticatorData: json['authenticatorData'] as String?,
    );

Map<String, dynamic> _$WebAuthnAttestationResponseModelToJson(
        WebAuthnAttestationResponseModel instance) =>
    <String, dynamic>{
      'attestationObject': instance.attestationObject,
      'clientDataJSON': instance.clientDataJSON,
      'signature': instance.signature,
      'userHandle': instance.userHandle,
      'authenticatorData': instance.authenticatorData,
    };
