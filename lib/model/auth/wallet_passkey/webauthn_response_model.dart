import 'package:json_annotation/json_annotation.dart';

part 'webauthn_response_model.g.dart';

@JsonSerializable()
class WebAuthnResponseModel {
  const WebAuthnResponseModel({
    required this.id,
    required this.rawId,
    required this.response,
    required this.type,
  });

  final String id;
  final String rawId;
  final WebAuthnAttestationResponseModel response;
  final String type;

  factory WebAuthnResponseModel.fromJson(Map<String, dynamic> json) =>
      _$WebAuthnResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$WebAuthnResponseModelToJson(this);
}

@JsonSerializable()
class WebAuthnAttestationResponseModel {
  const WebAuthnAttestationResponseModel({
      this.attestationObject,
      this.clientDataJSON,
    this.signature,
    this.userHandle,
    this.authenticatorData,
  });

  final String? attestationObject;
  final String? clientDataJSON;
  final String? signature;
  final String? userHandle;
 
  final String? authenticatorData;

  factory WebAuthnAttestationResponseModel.fromJson(
    Map<String, dynamic> json,
  ) => _$WebAuthnAttestationResponseModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$WebAuthnAttestationResponseModelToJson(this);
}
