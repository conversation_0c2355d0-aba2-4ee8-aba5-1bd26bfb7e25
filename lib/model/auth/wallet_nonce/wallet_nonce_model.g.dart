// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet_nonce_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WalletNonceModel _$WalletNonceModelFromJson(Map<String, dynamic> json) =>
    WalletNonceModel(
      message: json['message'] as String,
    );

Map<String, dynamic> _$WalletNonceModelToJson(WalletNonceModel instance) =>
    <String, dynamic>{
      'message': instance.message,
    };

WalletRegisterNonceModel _$WalletRegisterNonceModelFromJson(
        Map<String, dynamic> json) =>
    WalletRegisterNonceModel(
      signature: json['signature'] as String,
      networkBase: json['networkBase'] as String,
      address: json['address'] as String,
      walletProvider: json['walletProvider'] as String,
    );

Map<String, dynamic> _$WalletRegisterNonceModelToJson(
        WalletRegisterNonceModel instance) =>
    <String, dynamic>{
      'networkBase': instance.networkBase,
      'address': instance.address,
      'walletProvider': instance.walletProvider,
      'signature': instance.signature,
    };
