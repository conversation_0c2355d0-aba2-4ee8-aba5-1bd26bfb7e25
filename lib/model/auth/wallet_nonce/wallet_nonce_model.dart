import 'package:json_annotation/json_annotation.dart';

part 'wallet_nonce_model.g.dart';

@JsonSerializable()
class WalletNonceModel {
  const WalletNonceModel({required this.message});

  final String message;
  factory WalletNonceModel.fromJson(Map<String, dynamic> json) =>
      _$WalletNonceModelFromJson(json);

  Map<String, dynamic> toJson() => _$WalletNonceModelToJson(this);
}

@JsonSerializable()
class WalletRegisterNonceModel {
  const WalletRegisterNonceModel({
    required this.signature,
    required this.networkBase,
    required this.address,
    required this.walletProvider,
  });
  final String networkBase;
  final String address;
  final String walletProvider;
  final String signature;

  factory WalletRegisterNonceModel.fromJson(Map<String, dynamic> json) =>
      _$WalletRegisterNonceModelFromJson(json);

  Map<String, dynamic> toJson() => _$WalletRegisterNonceModelToJson(this);
}
