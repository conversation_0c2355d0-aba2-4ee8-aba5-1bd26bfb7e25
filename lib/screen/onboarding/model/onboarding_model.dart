import 'package:toii_mesh/gen/assets.gen.dart';
import 'package:toii_mesh/generated/l10n.dart';
 

final List<OnboardingModel> listOnboarding = [
  OnboardingModel(
    image: Assets.images.image1,
    message: S.current.onboarding_welcome,
    message1: S.current.onboarding_toii_social,
    description: S.current.onboarding_description1,
  ),
  OnboardingModel(
    image: Assets.images.image2,
    message: S.current.onboarding_welcome,
    message1: S.current.onboarding_toii_social,
    description: S.current.onboarding_description2,
  ),
  OnboardingModel(
    image: Assets.images.image3,
    message: S.current.onboarding_welcome,
    message1: S.current.onboarding_toii_social,
    description: S.current.onboarding_description3,
  ),
];

class OnboardingModel {
  final AssetGenImage image;
  final String message;
  final String message1;
  final String description;
  OnboardingModel({
    required this.image,
    required this.message,
    required this.message1,
    required this.description,
  });
}
