import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/cubit/auth/login/login/login_cubit.dart';
import 'package:toii_mesh/cubit/theme/theme_cubit.dart';
import 'package:toii_mesh/router/app_router.dart';
import 'package:toii_mesh/screen/onboarding/model/onboarding_model.dart';
import 'package:toii_mesh/screen/onboarding/widget/dot_page_indicator.dart';
import 'package:toii_mesh/widget/button/button.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';
import 'package:toii_mesh/widget/snack_bar/snackbar.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  final _pageController = PageController();

  late List<Widget> _pages;

  late TabController _selectorController;

  int _currentPage = 0;

  Duration get _duration => const Duration(milliseconds: 800);

  @override
  void initState() {
    _pages = listOnboarding.map((e) => _itemOnboarding(e)).toList();

    _selectorController = TabController(length: _pages.length, vsync: this);

    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _selectorController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    _currentPage = index;
    _selectorController.animateTo(index, duration: _duration);
  }

  void _changePage(int index) {
    if (!_pageController.hasClients) return;

    index == 0
        ? _pageController.jumpToPage(index)
        : _pageController.animateToPage(
            index,
            duration: _duration,
            curve: Curves.ease,
          );
  }

  Widget _itemOnboarding(OnboardingModel item) {
    return Stack(
      children: [
        item.image.image(fit: BoxFit.fitWidth, width: double.infinity),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 91),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const SizedBox(width: 16),
                Text(
                  item.message,
                  style: headlineSmall.copyColor(themeData.neutral500),
                ),
                Text(
                  item.message1,
                  style: headlineLarge.copyColor(themeData.primaryGreen500),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Text(
                item.description,
                style: displayMedium.copyWith(
                  fontSize: 42,
                  height: 52 / 42,
                  color: themeData.black800,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit(),
      child: Scaffold(
        body: Stack(children: [_carouselSliderWidget(), _nextButton()]),
      ),
    );
  }

  Widget _carouselSliderWidget() {
    return PageView.builder(
      physics: const AlwaysScrollableScrollPhysics(),
      controller: _pageController,
      itemCount: _pages.length,
      onPageChanged: _onPageChanged,
      itemBuilder: (BuildContext context, int index) {
        return _pages[index % _pages.length];
      },
    );
  }

  Widget _nextButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Expanded(child: Container()),
        DotPageIndicator(tabController: _selectorController),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          width: double.infinity,
          height: 82 + MediaQuery.of(context).padding.bottom,
          //     color: themeData.primaryGreen500,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: TSButton.primary(
                  size: ButtonSize.block,
                  onPressed: () async {
                    // final result = await context.push(
                    //   RouterEnums.termAndCondition.routeName,
                    // );
                    // if (result == true) {
                    //   context.push(RouterEnums.createAcount.routeName);
                    // }
                    context.push(RouterEnums.createAcount.routeName);
                  },
                  title: 'JOIN GAO SOCIAL',
                ),
              ),
              const SizedBox(width: 2),
              BlocConsumer<LoginCubit, LoginState>(
                listener: (context, state) {
                  if (state.status.isSuccess) {
                    context.go(RouterEnums.inital.routeName);
                  }
                  if (state.status.isFailure) {
                    context.showSnackbar(message: state.message ?? "");
                  }
                },
                builder: (context, state) {
                  return Builder(
                    builder: (context) {
                      return GestureDetector(
                        onTap: () {
                          context.read<LoginCubit>().getProfileByPassKey();
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 22),
                          alignment: Alignment.center,
                          height: 48,
                          decoration: BoxDecoration(
                            color: themeData.black800,
                            borderRadius: BorderRadius.circular(100),
                          ),
                          child: Text(
                            'Restore',
                            style: titleMedium.copyColor(themeData.white900),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
