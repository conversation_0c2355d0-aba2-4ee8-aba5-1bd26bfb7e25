import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/gen/assets.gen.dart';
import 'package:toii_mesh/router/app_router.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';

class PeopleTabScreen extends StatefulWidget {
  const PeopleTabScreen({super.key});

  @override
  State<PeopleTabScreen> createState() => _PeopleTabScreenState();
}

class _PeopleTabScreenState extends State<PeopleTabScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          // Status Bar (handled by system)

          // Main Content
          Expanded(
            child: Column(
              children: [
                const SizedBox(height: 56), // Status bar space

                // Search Bar
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                  decoration: BoxDecoration(
                    color: context.themeData.neutral100,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    children: [
                      Assets.icons.icSearch.svg(
                        width: 18,
                        height: 18,
                        colorFilter: ColorFilter.mode(
                          context.themeData.neutral400,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Username',
                          style: bodyLarge.copyWith(
                            color: context.themeData.neutral400,
                          ),
                        ),
                      ),
                      Container(
                        width: 1,
                        height: 24,
                        color: context.themeData.neutral200,
                      ),
                      const SizedBox(width: 12),
                      Assets.icons.icQrCode.svg(
                        width: 22,
                        height: 22,
                        colorFilter: ColorFilter.mode(
                          context.themeData.neutral400,
                          BlendMode.srcIn,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 8),

                // Action Buttons
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      _buildActionButton(
                        'Find friends nearby',
                        Assets.icons.icFindNearby.svg(
                          width: 16,
                          height: 16,
                          colorFilter: const ColorFilter.mode(
                            Colors.white,
                            BlendMode.srcIn,
                          ),
                        ),
                        context.themeData.foundationBlue800,
                        () {
                          // TODO: Find friends nearby
                        },
                      ),
                      const SizedBox(height: 8),
                      _buildActionButton(
                        'Share link',
                        Assets.icons.icShareLink.svg(
                          width: 16,
                          height: 16,
                          colorFilter: const ColorFilter.mode(
                            Colors.white,
                            BlendMode.srcIn,
                          ),
                        ),
                        context.themeData.foundationPink700,
                        () {
                          // TODO: Share link
                        },
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 4,
                        decoration: BoxDecoration(
                          color: context.themeData.neutral100,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 14),

                // Suggestions Section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          'Suggestions',
                          style: titleLarge.copyWith(
                            color: context.themeData.neutral800,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(height: 14),
                      Expanded(
                        child: ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: _figmaPeople.length,
                          itemBuilder: (context, index) {
                            final person = _figmaPeople[index];
                            return _buildFigmaPersonItem(person);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
      String title, Widget icon, Color backgroundColor, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: icon,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: bodyLarge.copyWith(
                  color: context.themeData.neutral800,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFigmaPersonItem(FigmaPerson person) {
    return GestureDetector(
      onTap: () {
        // Navigate to user profile screen
        context.push(
          RouterEnums.userProfile.routeName,
          extra: {
            'figmaPerson': person,
          },
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            // Avatar with online indicator
            Stack(
              children: [
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(28),
                    image: DecorationImage(
                      image: AssetImage(person.avatarPath),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Positioned(
                  right: 0,
                  bottom: 3,
                  child: Container(
                    width: 10,
                    height: 10,
                    decoration: BoxDecoration(
                      color: context.themeData.primaryBlue500,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(
                        color: Colors.white,
                        width: 3,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(width: 12),

            // Person info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    person.name,
                    style: titleMedium.copyWith(
                      color: context.themeData.neutral800,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    'Username',
                    style: bodyLarge.copyWith(
                      color: context.themeData.neutral400,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 83), // Spacing from Figma

            // Action button
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: person.buttonType == 'primary'
                    ? context.themeData.primaryBlue500
                    : context.themeData.black800,
                borderRadius: BorderRadius.circular(48),
              ),
              child: Text(
                person.buttonText,
                style: titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Figma-based data models
class FigmaPerson {
  final String name;
  final String avatarPath;
  final String buttonText;
  final String buttonType; // 'primary' or 'secondary'

  FigmaPerson({
    required this.name,
    required this.avatarPath,
    required this.buttonText,
    required this.buttonType,
  });
}

final List<FigmaPerson> _figmaPeople = [
  FigmaPerson(
    name: 'Kristin Watson',
    avatarPath: 'assets/images/avatar_kristin_watson.png',
    buttonText: 'Add friend',
    buttonType: 'primary',
  ),
  FigmaPerson(
    name: 'Theresa Webb',
    avatarPath: 'assets/images/avatar_theresa_webb.png',
    buttonText: 'Cancel',
    buttonType: 'secondary',
  ),
  FigmaPerson(
    name: 'Albert Flores',
    avatarPath: 'assets/images/avatar_albert_flores.png',
    buttonText: 'Add friend',
    buttonType: 'primary',
  ),
];
