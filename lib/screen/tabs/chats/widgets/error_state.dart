import 'package:flutter/material.dart';

/// Error state widget for XMTP errors
class ChatErrorState extends StatelessWidget {
  final String message;
  final VoidCallback? onRetryPressed;

  const ChatErrorState({
    super.key,
    required this.message,
    this.onRetryPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Connection Error',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Color(0xFF292929),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF777777),
              ),
            ),
            const SizedBox(height: 24),
            if (onRetryPressed != null)
              ElevatedButton(
                onPressed: onRetryPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6C4EFF),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Retry'),
              ),
          ],
        ),
      ),
    );
  }
}
