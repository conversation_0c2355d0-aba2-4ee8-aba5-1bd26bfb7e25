import 'package:flutter/material.dart';
import 'package:toii_mesh/model/chat/chat_models.dart';
import 'conversation_item.dart';

/// Widget for displaying list of conversations
class ConversationList extends StatelessWidget {
  final List<ChatConversation> conversations;
  final Function(ChatConversation)? onConversationTap;

  const ConversationList({
    super.key,
    required this.conversations,
    this.onConversationTap,
  });

  @override
  Widget build(BuildContext context) {
    if (conversations.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Color(0xFF777777),
            ),
            SizedBox(height: 16),
            Text(
              'No conversations yet',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Color(0xFF292929),
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Start a new conversation',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF777777),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: conversations.length,
      itemBuilder: (context, index) {
        final conversation = conversations[index];
        return ConversationItem(
          conversation: conversation,
          onTap: () => onConversationTap?.call(conversation),
        );
      },
    );
  }
}
