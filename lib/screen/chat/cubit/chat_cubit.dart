import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/model/chat/chat_models.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart'
    hide MessageDeliveryStatus;
import 'package:uuid/uuid.dart';

import 'chat_state.dart';

class ChatCubit extends Cubit<ChatState> {
  final XmtpCubit _xmtpCubit;
  final String _currentUserInboxId;
  StreamSubscription<XmtpMessage>? _messageStreamSubscription;
  Timer? _refreshTimer;

  ChatCubit({
    required XmtpCubit xmtpCubit,
    required String currentUserInboxId,
  })  : _xmtpCubit = xmtpCubit,
        _currentUserInboxId = currentUserInboxId,
        super(const ChatInitial());

  /// Load chat conversation and messages
  Future<void> loadChat(ChatConversation conversation) async {
    try {
      emit(const ChatLoading());

      if (!_xmtpCubit.hasClient) {
        emit(const ChatError(message: 'XMTP client not initialized'));
        return;
      }

      // Load message history
      final messages = await _loadMessages(conversation.id);

      emit(ChatLoaded(conversation: conversation, messages: messages));

      // Start message streaming
      await _startMessageStreaming(conversation.id);

      // Start periodic refresh as backup
      _startPeriodicRefresh(conversation.id);
    } catch (e) {
      log('ChatCubit: Error loading chat: $e');
      emit(ChatError(
        message: 'Failed to load chat: $e',
        conversationId: conversation.id,
      ));
    }
  }

  /// Load messages for a conversation
  Future<List<ChatMessage>> _loadMessages(String conversationId) async {
    try {
      log('ChatCubit: Loading messages for conversation: $conversationId');

      final xmtpPlugin = ToiiXmtpFlutter();
      final xmtpMessages = await xmtpPlugin.getMessages(conversationId);

      log('ChatCubit: Retrieved ${xmtpMessages.length} XMTP messages');

      final chatMessages = <ChatMessage>[];
      for (final msg in xmtpMessages) {
        try {
          if (msg.id.isEmpty) {
            log('ChatCubit: Skipping message with empty ID');
            continue;
          }

          final chatMessage = ChatMessage.fromXmtpMessage(
            msg,
            _currentUserInboxId,
          );
          chatMessages.add(chatMessage);
        } catch (e) {
          log('ChatCubit: Error converting message ${msg.id}: $e');
        }
      }

      // Sort by sent time (newest first for chat UI)
      chatMessages.sort((a, b) => b.sentAt.compareTo(a.sentAt));

      log('ChatCubit: Successfully loaded ${chatMessages.length} chat messages');
      return chatMessages;
    } catch (e) {
      log('ChatCubit: Error loading messages: $e');
      return [];
    }
  }

  /// Send a message
  Future<void> sendMessage(String content) async {
    final currentState = state;
    if (currentState is! ChatLoaded) return;

    try {
      // Create pending message
      final pendingMessageId = const Uuid().v4();
      final pendingMessage = ChatMessage(
        id: pendingMessageId,
        conversationId: currentState.conversation.id,
        senderInboxId: _currentUserInboxId,
        content: content,
        sentAt: DateTime.now(),
        deliveryStatus: MessageDeliveryStatus.pending,
        isFromCurrentUser: true,
      );

      // Add pending message to UI
      final updatedMessages = [pendingMessage, ...currentState.messages];
      emit(ChatSendingMessage(
        conversation: currentState.conversation,
        messages: updatedMessages,
        pendingMessageId: pendingMessageId,
        isStreaming: currentState.isStreaming,
      ));

      // Send message via XMTP
      final xmtpPlugin = ToiiXmtpFlutter();
      final sentMessageId = await xmtpPlugin.sendMessage(
        conversationId: currentState.conversation.id,
        content: content,
      );

      // Update message with sent status
      final sentMessage = pendingMessage.copyWith(
        id: sentMessageId,
        deliveryStatus: MessageDeliveryStatus.sent,
      );

      final finalMessages =
          updatedMessages.where((msg) => msg.id != pendingMessageId).toList();
      finalMessages.insert(0, sentMessage);

      emit(ChatMessageSent(
        conversation: currentState.conversation,
        messages: finalMessages,
        sentMessageId: sentMessageId,
        isStreaming: currentState.isStreaming,
      ));
    } catch (e) {
      log('ChatCubit: Error sending message: $e');

      // Handle send error - mark message as failed
      final currentStateAfterError = state;
      if (currentStateAfterError is ChatSendingMessage) {
        final failedMessages = currentStateAfterError.messages.map((msg) {
          if (msg.id == currentStateAfterError.pendingMessageId) {
            return msg.copyWith(deliveryStatus: MessageDeliveryStatus.failed);
          }
          return msg;
        }).toList();

        emit(ChatLoaded(
          conversation: currentStateAfterError.conversation,
          messages: failedMessages,
          isStreaming: currentStateAfterError.isStreaming,
        ));
      }

      emit(ChatError(
        message: 'Failed to send message: $e',
        conversationId: currentState.conversation.id,
      ));
    }
  }

  /// Start message streaming
  Future<void> _startMessageStreaming(String conversationId) async {
    try {
      await _stopMessageStreaming();

      final xmtpPlugin = ToiiXmtpFlutter();
      final messageStream = xmtpPlugin.streamMessages(conversationId);

      _messageStreamSubscription = messageStream.listen(
        (xmtpMessage) => _handleNewMessage(xmtpMessage),
        onError: (error) {
          log('ChatCubit: Message streaming error: $error');
          // Try to restart streaming after a delay on error
          Future.delayed(const Duration(seconds: 5), () {
            if (!isClosed) {
              _startMessageStreaming(conversationId);
            }
          });
        },
        cancelOnError: false,
      );

      log('ChatCubit: Started message streaming for conversation: $conversationId');
    } catch (e) {
      log('ChatCubit: Failed to start message streaming: $e');
    }
  }

  /// Handle new message from stream
  void _handleNewMessage(XmtpMessage xmtpMessage) {
    try {
      final currentState = state;
      if (currentState is! ChatLoaded) return;

      if (xmtpMessage.id.isEmpty) {
        log('ChatCubit: Skipping message with empty ID');
        return;
      }

      // Skip if message is from current user (already handled by send)
      if (xmtpMessage.senderInboxId == _currentUserInboxId) {
        log('ChatCubit: Skipping own message: ${xmtpMessage.id}');
        return;
      }

      // Check if message already exists
      final existingMessage = currentState.messages.any(
        (msg) => msg.id == xmtpMessage.id,
      );
      if (existingMessage) {
        log('ChatCubit: Message already exists: ${xmtpMessage.id}');
        return;
      }

      log('ChatCubit: Processing new message: ${xmtpMessage.id}');

      final newMessage = ChatMessage.fromXmtpMessage(
        xmtpMessage,
        _currentUserInboxId,
      );

      // Insert new message at the beginning (newest first)
      final updatedMessages = [newMessage, ...currentState.messages];

      emit(ChatMessageReceived(
        conversation: currentState.conversation,
        messages: updatedMessages,
        newMessage: newMessage,
        isStreaming: currentState.isStreaming,
      ));

      log('ChatCubit: New message added to chat: ${newMessage.content}');
    } catch (e) {
      log('ChatCubit: Error handling new message: $e');
    }
  }

  /// Start periodic refresh as backup for streaming
  void _startPeriodicRefresh(String conversationId) {
    _stopPeriodicRefresh();

    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      if (isClosed) {
        timer.cancel();
        return;
      }

      final currentState = state;
      if (currentState is ChatLoaded &&
          currentState.conversation.id == conversationId) {
        try {
          final latestMessages = await _loadMessages(conversationId);

          if (latestMessages.length > currentState.messages.length) {
            log('ChatCubit: Found new messages via refresh');
            emit(currentState.copyWith(messages: latestMessages));
          }
        } catch (e) {
          log('ChatCubit: Error during periodic refresh: $e');
        }
      }
    });
  }

  /// Stop periodic refresh
  void _stopPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Stop message streaming
  Future<void> _stopMessageStreaming() async {
    await _messageStreamSubscription?.cancel();
    _messageStreamSubscription = null;
  }

  /// Refresh messages
  Future<void> refreshMessages() async {
    final currentState = state;
    if (currentState is! ChatLoaded) return;

    try {
      final messages = await _loadMessages(currentState.conversation.id);
      emit(currentState.copyWith(messages: messages));
    } catch (e) {
      emit(ChatError(
        message: 'Failed to refresh messages: $e',
        conversationId: currentState.conversation.id,
      ));
    }
  }

  @override
  Future<void> close() async {
    await _stopMessageStreaming();
    _stopPeriodicRefresh();
    return super.close();
  }
}
