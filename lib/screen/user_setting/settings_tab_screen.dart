import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/cubit/auth/logout/logout_cubit.dart';
import 'package:toii_mesh/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_mesh/router/app_router.dart';
import 'package:toii_mesh/widget/images/cached_network_image_widget.dart';
import 'package:toii_mesh/widget/snack_bar/snackbar.dart';

class SettingsTabScreen extends StatefulWidget {
  const SettingsTabScreen({super.key});

  @override
  State<SettingsTabScreen> createState() => _SettingsTabScreenState();
}

class _SettingsTabScreenState extends State<SettingsTabScreen> {
  void _showLogoutConfirmation(void Function() onConfirmed) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Sign Out'),
          content:
              const Text('Are you sure you want to sign out of your account?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                onConfirmed();
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Sign Out'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => GetIt.instance<LogoutCubit>()),
        BlocProvider(
            create: (context) => GetIt.instance<ProfileCubit>()..getProfile()),
      ],
      child: Builder(
        builder: (context) {
          return BlocListener<LogoutCubit, LogoutState>(
            listener: (context, state) {
              if (state.status.isSuccess) {
                context.go(RouterEnums.inital.routeName);
                context.showSnackbar(message: 'Successfully signed out');
              } else if (state.status.isFailure) {
                context.showSnackbar(
                  message: state.errorMessage ?? 'Failed to sign out',
                );
              }
            },
            child: Scaffold(
              backgroundColor: const Color(0xFFF6F6F6), // neutral100
              body: SafeArea(
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            // User Profile Header
                            const UserProfileHeader(),
                            const SizedBox(height: 24),

                            // Clear Messages (Destructive Action)
                            SettingsSection(
                              items: [
                                SettingsItem(
                                  iconPath: 'assets/icons/ic_edit.svg',
                                  title: 'Clear messages',
                                  onTap: () => _showLogoutConfirmation(() {
                                    // Handle clear messages
                                  }),
                                  isDestructive: true,
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),

                            // Main Settings Group
                            SettingsSection(
                              items: [
                                SettingsItem(
                                  iconPath: 'assets/icons/ic_notification.svg',
                                  title: 'Notifications',
                                  onTap: () {},
                                ),
                                SettingsItem(
                                  iconPath: 'assets/icons/ic_block.svg',
                                  title: 'Blocked users',
                                  onTap: () {},
                                ),
                                SettingsItem(
                                  iconPath: 'assets/icons/ic_language.svg',
                                  title: 'Language',
                                  onTap: () {},
                                ),
                                SettingsItem(
                                  iconPath: 'assets/icons/ic_backup.svg',
                                  title: 'Backup & Restore',
                                  onTap: () {},
                                  isLast: true,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// User Profile Header Component
class UserProfileHeader extends StatelessWidget {
  const UserProfileHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileCubit, ProfileState>(
      builder: (context, state) {
        final user = state.userModel;
        final displayName = user?.username ?? user?.username ?? 'Robert Fox123';
        final username = user?.username ?? 'Robert Fox123';
        final avatarUrl = user?.avatarUrl;

        return GestureDetector(
          onTap: () {
            // Navigate to profile edit screen
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                // Avatar
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: const Color(0xFFF6F6F6),
                  ),
                  child: avatarUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: CachedNetworkImageWidget(
                            imageUrl: avatarUrl,
                            width: 40,
                            height: 40,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: const Color(0xFFF6F6F6),
                          ),
                          child: const Icon(
                            Icons.person,
                            color: Color(0xFF777777),
                            size: 20,
                          ),
                        ),
                ),
                const SizedBox(width: 12),

                // User Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              displayName,
                              style: const TextStyle(
                                fontFamily: 'IBM Plex Sans',
                                fontWeight: FontWeight.w500,
                                fontSize: 18,
                                height: 1.56,
                                letterSpacing: 0.18,
                                color: Color(0xFF292929),
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          SvgPicture.asset(
                            'assets/icons/ic_qr_code.svg',
                            width: 22,
                            height: 22,
                            colorFilter: const ColorFilter.mode(
                              Color(0xFF777777),
                              BlendMode.srcIn,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 2),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              '@$username',
                              style: const TextStyle(
                                fontFamily: 'IBM Plex Sans',
                                fontWeight: FontWeight.w400,
                                fontSize: 12,
                                height: 1.33,
                                color: Color(0xFF777777),
                              ),
                            ),
                          ),
                          SvgPicture.asset(
                            'assets/icons/ic_arrow_right.svg',
                            width: 16,
                            height: 16,
                            colorFilter: const ColorFilter.mode(
                              Color(0xFF777777),
                              BlendMode.srcIn,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// Settings Section Component
class SettingsSection extends StatelessWidget {
  final List<SettingsItem> items;

  const SettingsSection({
    super.key,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isLast = index == items.length - 1;

          return Column(
            children: [
              item,
              if (!isLast)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 12),
                  height: 1,
                  color: const Color(0xFFF0F0F0),
                ),
            ],
          );
        }).toList(),
      ),
    );
  }
}

// Settings Item Component
class SettingsItem extends StatelessWidget {
  final String iconPath;
  final String title;
  final VoidCallback onTap;
  final bool isDestructive;
  final bool isLast;

  const SettingsItem({
    super.key,
    required this.iconPath,
    required this.title,
    required this.onTap,
    this.isDestructive = false,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Icon
              Container(
                width: 24,
                height: 24,
                alignment: Alignment.center,
                child: SvgPicture.asset(
                  iconPath,
                  width: 20,
                  height: 20,
                  colorFilter: ColorFilter.mode(
                    isDestructive
                        ? const Color(0xFFD33636) // Foundation/Red/Red-500
                        : const Color(0xFFB5A6FF), // Primary/blue-200
                    BlendMode.srcIn,
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // Title
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'IBM Plex Sans',
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                    height: 1.25,
                    color: isDestructive
                        ? const Color(0xFFD33636) // Foundation/Red/Red-500
                        : const Color(0xFF292929), // Neutrals/Neutral 800 [day]
                  ),
                ),
              ),

              // Arrow
              SvgPicture.asset(
                'assets/icons/ic_arrow_right.svg',
                width: 16,
                height: 16,
                colorFilter: const ColorFilter.mode(
                  Color(0xFFAFAFAF), // Neutrals/Neutral 300 [day]
                  BlendMode.srcIn,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
