enum Flavor { dev, prod }

class F {
  static Flavor? appFlavor;

  static String get name => appFlavor?.name ?? '';

  static String get title {
    switch (appFlavor) {
      case Flavor.dev:
        return 'Flavor Sample App Dev';
      case Flavor.prod:
        return 'Flavor Sample App';
      default:
        return 'title';
    }
  }

  static String get url {
    switch (appFlavor) {
      case Flavor.dev:
        return 'https://api-dev.toii.social';
      case Flavor.prod:
        return 'https://api.gao.social';
      default:
        return 'https://api.gao.social';
    }
  }

  static String get rpIDWeb {
    switch (appFlavor) {
      case Flavor.dev:
        return 'app-dev.gao.social';
      case Flavor.prod:
        return 'app.gao.social';
      default:
        return 'app-dev.gao.social';
    }
  }
}
