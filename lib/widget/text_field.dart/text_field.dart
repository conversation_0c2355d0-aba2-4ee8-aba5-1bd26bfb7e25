import 'package:flutter/material.dart';
import 'package:toii_mesh/cubit/theme/theme_cubit.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/text_field.dart/custom_outline.dart';
 

class TTextField extends StatefulWidget {
  final String? hintText;
  final String? labelText;
  final TextEditingController? textController;
  final bool obscureText;
  final FocusNode? focusNode;
  final String? errorText;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onChanged;
  final EdgeInsets? contentPadding;
  final Function(String?)? onSaved;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final bool autofocus;
  final bool showBorder;
  final TextStyle? hintStyle;
  final TextStyle? textStyle;
  final int? maxLines;
  final Color? fillColor;

  const TTextField({
    super.key,
    this.hintText,
    this.labelText,
    this.textController,
    this.obscureText = false,
    this.focusNode,
    this.errorText,
    this.onEditingComplete,
    this.onChanged,
    this.contentPadding,
    this.onSaved,
    this.suffixIcon,
    this.prefixIcon,
    this.autofocus = false,
    this.showBorder = true,
    this.hintStyle,
    this.textStyle,
    this.maxLines = 1,
    this.fillColor,
  });

  @override
  State<TTextField> createState() => _TTextFieldState();
}

class _TTextFieldState extends State<TTextField> {
  @override
  Widget build(BuildContext context) {
    return TextField(
      autofocus: widget.autofocus,
      onEditingComplete: widget.onEditingComplete,
      focusNode: widget.focusNode,
      controller: widget.textController,
      onChanged: widget.onChanged,
      obscureText: widget.obscureText,
      style: widget.textStyle,
      maxLines: widget.maxLines,

      decoration: InputDecoration(
        contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        hintMaxLines: 1,
        hintStyle: widget.hintStyle,
        isDense: true,
        filled: true,

        prefixIcon: widget.prefixIcon,
        suffixIcon: widget.suffixIcon,
        errorText: widget.errorText,
        errorMaxLines: 2,
        labelText: widget.labelText,
        hintText: widget.labelText == null ? widget.hintText : null,
        fillColor: widget.fillColor ?? themeData.white50,
        labelStyle: WidgetStateTextStyle.resolveWith((Set<WidgetState> states) {
          final Color color =
              states.contains(WidgetState.error)
                  ? (widget.textController?.text ?? "").isEmpty
                      ? themeData.neutral400
                      : themeData.red500
                  : states.contains(WidgetState.focused)
                  ? themeData.primaryGreen500
                  : themeData.neutral400;
          return TextStyle(color: color);
        }),
        floatingLabelStyle: WidgetStateTextStyle.resolveWith((
          Set<WidgetState> states,
        ) {
          final Color color =
              states.contains(WidgetState.error)
                  ? (widget.textController?.text ?? "").isEmpty
                      ? themeData.neutral400
                      : themeData.red500
                  : states.contains(WidgetState.focused)
                  ? themeData.primaryGreen500
                  : themeData.neutral400;
          return TextStyle(color: color);
        }),
        border:
            widget.showBorder
                ? InnerLabelOutlineInputBorder(
                  borderSide: BorderSide(color: themeData.neutral200),
                  borderRadius: BorderRadius.all(Radius.circular(8.0)),
                )
                : InputBorder.none,
        focusedBorder:
            widget.showBorder
                ? InnerLabelOutlineInputBorder(
                  borderSide: BorderSide(color: themeData.primaryGreen500),
                  borderRadius: BorderRadius.all(Radius.circular(8.0)),
                )
                : InputBorder.none,
        errorBorder:
            widget.showBorder
                ? InnerLabelOutlineInputBorder(
                  borderSide: BorderSide(color: themeData.red500),
                  borderRadius: BorderRadius.all(Radius.circular(8.0)),
                )
                : InputBorder.none,
        focusedErrorBorder:
            widget.showBorder
                ? InnerLabelOutlineInputBorder(
                  borderSide: BorderSide(color: themeData.red500),
                  borderRadius: BorderRadius.all(Radius.circular(8.0)),
                )
                : InputBorder.none,
        enabledBorder:
            widget.showBorder
                ? InnerLabelOutlineInputBorder(
                  borderSide: BorderSide(color: themeData.neutral200),
                  borderRadius: BorderRadius.all(Radius.circular(8.0)),
                )
                : InputBorder.none,
        errorStyle: const TextStyle(height: 0),
      ),
    );
  }
}
