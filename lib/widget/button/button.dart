import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:toii_mesh/cubit/theme/theme_cubit.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';
 
enum ButtonSize { large, small, block }

enum ButtonVariant { primary, secondary, tertiary, black }

extension ButtonSizeExtension on ButtonSize {
  double get height {
    switch (this) {
      case ButtonSize.large:
        return 56;
      case ButtonSize.small:
        return 40;
      case ButtonSize.block:
        return 48;
    }
  }

  // double get paddingVertical => this == ButtonSize.large ? 16 : 6;
  // double get paddingHorizontal => this == ButtonSize.large ? 20 : 12;
  // double get iconSize => this == ButtonSize.large ? 24 : 16;
  // double get spaceBetweenIconAndText => this == ButtonSize.large ? 12 : 8;
}

extension ButtonVariantExtension on ButtonVariant {
  Color get backgroundColor {
    switch (this) {
      case ButtonVariant.primary:
        return themeData.primaryGreen500;
      case ButtonVariant.secondary:
        return themeData.white200;
      case ButtonVariant.tertiary:
        return themeData.primaryGreen500;
      case ButtonVariant.black:
        return themeData.neutral800;
    }
  }
}

class TSButton extends StatelessWidget {
  final ButtonSize size;
  final ButtonVariant? elevatedVariant;
  final String title;
  final bool isLoading;
  final VoidCallback onPressed;
  final bool isEnabled;
  final bool isHideKeyBoard;
  final Color? backgroundColor;
  factory TSButton.primary({
    required String title,
    bool isLoading = false,
    bool isEnabled = true,
    ButtonSize size = ButtonSize.large,
    required VoidCallback onPressed,
    bool isHideKeyBoard = false,
  }) {
    return TSButton(
      title: title,
      size: size,
      isEnabled: isEnabled,
      isLoading: isLoading,
      elevatedVariant: ButtonVariant.primary,
      onPressed: onPressed,
    );
  }

  factory TSButton.secondary({
    Color? backgroundColor,
    required String title,
    bool isLoading = false,
    ButtonSize size = ButtonSize.large,
    required VoidCallback onPressed,
    bool isEnabled = true,
    bool isHideKeyBoard = false,
  }) {
    return TSButton(
      title: title,
      size: size,
      isLoading: isLoading,
      elevatedVariant: ButtonVariant.secondary,
      onPressed: onPressed,
      isEnabled: isEnabled,
      isHideKeyBoard: isHideKeyBoard,
      backgroundColor: backgroundColor,
    );
  }
  const TSButton({
    super.key,
    this.size = ButtonSize.block,
    this.elevatedVariant,
    this.isLoading = false,
    required this.title,
    required this.onPressed,
    this.isEnabled = true,
    this.isHideKeyBoard = false,
    this.backgroundColor,
  });

  Color get color {
    if (isEnabled) {
      if (backgroundColor != null) {
        return backgroundColor!;
      }
      if (elevatedVariant != null) {
        return elevatedVariant!.backgroundColor;
      }
      return themeData.primaryGreen500;
    } else {
      return themeData.neutral100;
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(100),
      onTap:
          isEnabled
              ? () {
                if (isHideKeyBoard) {
                  SystemChannels.textInput.invokeMethod('TextInput.hide');
                }
                onPressed();
              }
              : null,
      child: Container(
        height: size.height,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(100),
        ),
        child: Center(
          child:
              isLoading
                  ? CircularProgressIndicator(
                    strokeWidth: 2,
                    backgroundColor: themeData.white900,
                    color: themeData.primaryGreen500,
                  )
                  : Text(
                    title,
                    style: titleMedium.copyWith(
                      color:
                          (elevatedVariant == ButtonVariant.black)
                              ? themeData.textContrast
                              : (isEnabled
                                  ? themeData.white900
                                  : themeData.neutral300),
                    ),
                  ),
        ),
      ),
    );
  }
}
