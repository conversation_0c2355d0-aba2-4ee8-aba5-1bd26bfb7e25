import 'package:animated_bottom_navigation_bar/animated_bottom_navigation_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

enum BottomNavTab {
  chats(0, 'Chats', 'assets/icons/ic_chat.svg'),
  contacts(1, 'Contacts', 'assets/icons/ic_contacts.svg'),
  people(2, 'People', 'assets/icons/ic_search.svg'),
  settings(3, 'Setting', 'assets/icons/ic_settings.svg');

  const BottomNavTab(this.tabIndex, this.label, this.iconPath);

  final int tabIndex;
  final String label;
  final String iconPath;
}

class CustomBottomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBottomNavigationBar.builder(
      itemCount: BottomNavTab.values.length,
      tabBuilder: (int index, bool isActive) {
        final tab = BottomNavTab.values[index];
        return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              tab.iconPath,
              width: 24,
              height: 24,
              colorFilter: ColorFilter.mode(
                isActive
                    ? const Color(0xFF6C4EFF) // Primary blue
                    : const Color(0xFFAFAFAF), // Neutral 300 (inactive)
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              tab.label,
              style: TextStyle(
                fontFamily: 'SF Pro Text',
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: isActive
                    ? const Color(0xFF6C4EFF) // Primary blue
                    : const Color(0xFFAFAFAF), // Neutral 300 (inactive)
              ),
            ),
          ],
        );
      },
      backgroundColor: Colors.white.withValues(alpha: 0.95),
      activeIndex: currentIndex,
      splashColor: const Color(0xFF6C4EFF).withValues(alpha: 0.1),
      notchSmoothness: NotchSmoothness.verySmoothEdge,
      gapLocation: GapLocation.center, // Center gap for FAB
      gapWidth: 40, // Width of the gap for the indicator
      leftCornerRadius: 16,
      rightCornerRadius: 16,
      onTap: onTap,
      height: 75,
      elevation: 8,
      shadow: BoxShadow(
        color: Colors.black.withValues(alpha: 0.04),
        offset: const Offset(0, -18),
        blurRadius: 29,
        spreadRadius: 0,
      ),
    );
  }
}

// Home indicator widget to match the Figma design
class HomeIndicator extends StatelessWidget {
  const HomeIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 34,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.95), // More opaque, no blur
      ),
      child: Center(
        child: Container(
          width: 148,
          height: 5,
          decoration: BoxDecoration(
            color: const Color(0xFF090A0A), // Ink/Darkest
            borderRadius: BorderRadius.circular(100),
          ),
        ),
      ),
    );
  }
}
