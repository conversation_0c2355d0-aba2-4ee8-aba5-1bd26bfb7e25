import 'package:flutter/material.dart';
import 'package:toii_mesh/cubit/theme/theme_cubit.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';
 
class BaseBottomSheet extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final bool isShowClose;
  final String? title;
  final double? initHeightChild;
  final bool allowScroll;
  const BaseBottomSheet({
    super.key,
    required this.child,
    this.padding,
    this.isShowClose = false,
    this.title,
    this.initHeightChild,
    this.allowScroll = true,
  });

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.sizeOf(context).height * 0.9,
      ),
      child: SingleChildScrollView(
        physics:
            allowScroll
                ? const BouncingScrollPhysics()
                : const NeverScrollableScrollPhysics(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // isShowClose
            //     ? GestureDetector(
            //         onTap: () {
            //           Navigator.of(context).pop();
            //         },
            //         child: SizedBox(
            //           height: 48,
            //           child: Align(
            //             alignment: Alignment.centerRight,
            //             child: Container(
            //               margin: const EdgeInsets.only(right: 12),
            //               width: 32,
            //               height: 32,
            //               decoration: BoxDecoration(
            //                   color: AppColors.neutral50,
            //                   borderRadius: BorderRadius.circular(20)),
            //               child: Center(
            //                   child:
            //                       SvgPicture.asset(Assets.icons.icCloseSmall)),
            //             ),
            //           ),
            //         ),
            //       )
            Column(
              children: [
                const SizedBox(height: 12),
                Center(
                  child: Container(
                    height: 4,
                    width: 40,
                    decoration: BoxDecoration(
                      color: themeData.neutral200,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                if (title != null) const SizedBox(height: 24),
              ],
            ),
            if (title != null) Text(title!, style: headlineLarge),
            if (title != null) const SizedBox(height: 24),
            SafeArea(
              child:
                  padding != null
                      ? Padding(
                        padding: padding!,
                        child: SizedBox(height: initHeightChild, child: child),
                      )
                      : Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 16,
                        ),
                        child: SizedBox(height: initHeightChild, child: child),
                      ),
            ),
          ],
        ),
      ),
    );
  }
}
