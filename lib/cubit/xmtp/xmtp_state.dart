import 'package:equatable/equatable.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

abstract class XmtpState extends Equatable {
  const XmtpState();

  @override
  List<Object?> get props => [];
}

class XmtpInitial extends XmtpState {
  const XmtpInitial();
}

class XmtpLoading extends XmtpState {
  const XmtpLoading();
}

class XmtpClientCreated extends XmtpState {
  final XmtpClient client;

  const XmtpClientCreated({required this.client});

  @override
  List<Object?> get props => [client];
}

class XmtpLoadingConversations extends XmtpState {
  const XmtpLoadingConversations();
}

class XmtpConversationsLoaded extends XmtpState {
  final XmtpClient client;
  final List<Conversation> conversations;

  const XmtpConversationsLoaded({
    required this.client,
    required this.conversations,
  });

  @override
  List<Object?> get props => [client, conversations];
}

class XmtpCreatingConversation extends XmtpState {
  const XmtpCreatingConversation();
}

class XmtpConversationCreated extends XmtpState {
  final Conversation conversation;

  const XmtpConversationCreated({required this.conversation});

  @override
  List<Object?> get props => [conversation];
}

class XmtpError extends XmtpState {
  final String message;

  const XmtpError({required this.message});

  @override
  List<Object?> get props => [message];
}
