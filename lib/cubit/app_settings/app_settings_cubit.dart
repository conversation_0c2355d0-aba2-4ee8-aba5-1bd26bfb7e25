import 'dart:io';

import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_mesh/core/repository/auth_repository.dart';
import 'package:toii_mesh/model/app_settings/app_settings_model.dart';
 
part 'app_settings_state.dart';

class AppSettingsCubit extends Cubit<AppSettingsState> {
  final  AuthRepository _appSettingsRepository;

  AppSettingsCubit({required AuthRepository appSettingsRepository})
      : _appSettingsRepository = appSettingsRepository,
        super(const AppSettingsState());

  Future<void> getActiveVersion() async {
    try {
      emit(state.copyWith(status: AppSettingsStatus.loading));
      
      // Determine platform
      String platform;
      if (kIsWeb) {
        platform = 'web';
      } else if (Platform.isAndroid) {
        platform = 'android';
      } else if (Platform.isIOS) {
        platform = 'ios';
      } else {
        platform = 'unknown';
      }

      final result = await _appSettingsRepository.getActiveVersion(platform);
      
      emit(state.copyWith(
        status: AppSettingsStatus.success,
        appSettings: result.data,
        errorMessage: null,
      ));
    } on DioException catch (e) {
      emit(state.copyWith(
        status: AppSettingsStatus.failure,
        errorMessage: e.response?.data?['message'] ?? e.message ?? 'Unknown error occurred',
      ));
    } on Exception catch (e) {
      emit(state.copyWith(
        status: AppSettingsStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

 
  bool get shouldForceUpdate => state.appSettings?.config?.forceUpdate ?? false;
  bool get allowGuestLogin => state.appSettings?.config?.onLoginGuest ?? false;
  String get currentVersion => state.appSettings?.version?.version ?? '';
}
