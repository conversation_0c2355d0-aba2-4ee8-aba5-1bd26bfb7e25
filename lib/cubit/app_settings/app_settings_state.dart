part of 'app_settings_cubit.dart';

enum AppSettingsStatus { initial, loading, success, failure }

extension AppSettingsStatusX on AppSettingsStatus {
  bool get isInitial => this == AppSettingsStatus.initial;
  bool get isLoading => this == AppSettingsStatus.loading;
  bool get isSuccess => this == AppSettingsStatus.success;
  bool get isFailure => this == AppSettingsStatus.failure;
}

final class AppSettingsState extends Equatable {
  final AppSettingsStatus status;
  final  AppSettingsDataModel? appSettings;
  final String? errorMessage;

  const AppSettingsState({
    this.status = AppSettingsStatus.initial,
    this.appSettings,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [status, appSettings, errorMessage];

  AppSettingsState copyWith({
    AppSettingsStatus? status,
    AppSettingsDataModel? appSettings,
    String? errorMessage,
  }) {
    return AppSettingsState(
      status: status ?? this.status,
      appSettings: appSettings ?? this.appSettings,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
