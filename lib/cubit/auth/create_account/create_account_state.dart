part of 'create_account_cubit.dart';

enum CreateAccountStatus { initial, loading, success, failure }

extension CreateAccountStatusX on CreateAccountStatus {
  bool get isInitial => this == CreateAccountStatus.initial;
  bool get isLoading => this == CreateAccountStatus.loading;
  bool get isSuccess => this == CreateAccountStatus.success;
  bool get isFailure => this == CreateAccountStatus.failure;
}

final class CreateAccountState {
  final CreateAccountStatus status;
  final String? message;
  final String? errorMessage;

  const CreateAccountState({
    this.status = CreateAccountStatus.initial,
    this.message,
    this.errorMessage,
  });

  CreateAccountState copyWith({
    CreateAccountStatus? status,
    String? message,
    String? errorMessage,
  }) {
    return CreateAccountState(
      status: status ?? this.status,
      message: message ?? this.message,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
