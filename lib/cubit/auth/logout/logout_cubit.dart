import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_mesh/core/constant/key_shared.dart';
import 'package:toii_mesh/utils/shared_prefs/shared_prefs.dart';

part 'logout_state.dart';

class LogoutCubit extends Cubit<LogoutState> {
  LogoutCubit() : super(const LogoutState());

  /// Logout user by clearing authentication state
  void logout() async {
    try {
      // Check if cubit is closed before emitting
      if (isClosed) return;

      emit(state.copyWith(status: LogoutStatus.loading));

      // Clear authentication data from SharedPreferences
      await SharedPref.setBool(KeyShared.isLogin, false);
      await SharedPref().clearKey(KeyShared.tokenKey);
      await SharedPref().clearKey(KeyShared.tmpTokenKey);
      await SharedPref().clearKey(KeyShared.walletCurrentKey);

      // Note: If there's a logout API endpoint in the future, call it here
      // await _authRepository.logout();

      // Check if cubit is closed before emitting
      if (!isClosed) {
        emit(state.copyWith(status: LogoutStatus.success));
      }
    } on Exception catch (e) {
      // Check if cubit is closed before emitting
      if (!isClosed) {
        emit(
          state.copyWith(
            status: LogoutStatus.failure,
            errorMessage: e.toString(),
          ),
        );
      }
    }
  }

  /// Reset logout state to initial
  void resetState() {
    if (!isClosed) {
      emit(const LogoutState());
    }
  }
}
