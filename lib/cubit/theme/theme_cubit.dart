import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_mesh/core/constant/key_shared.dart';
import 'package:toii_mesh/utils/shared_prefs/shared_prefs.dart';
 

//import 'package:vp_design_system/vp_design_system.dart';

import 'theme_service.dart';

part 'theme_state.dart';

enum AppTheme { light, dark }

class ThemeCubit extends Cubit<ThemeState> {
  ThemeCubit() : super(ThemeInitial()) {
    init();
  }

  ThemeData _themeData = ThemeService.lightTheme;

  ThemeData get themeData => _themeData;

  AppTheme _currentTheme = AppTheme.light;
  AppTheme get currentTheme => _currentTheme;
  void toggleTheme(AppTheme data) {
    _currentTheme = data;
    _themeData =
        _currentTheme == AppTheme.light
            ? ThemeService.lightTheme
            : ThemeService.darkTheme;
    SharedPref.setString(KeyShared.currentThemeApp, _currentTheme.name);
    emit(ThemeChange(_currentTheme));
  }

  void init() {
    final data = SharedPref.getString(
      KeyShared.currentThemeApp,
      AppTheme.dark.name,
    );
    data == AppTheme.light.name ? AppTheme.light : AppTheme.dark;
    toggleTheme(_currentTheme);
  }
}

ThemeData get themeData => GetIt.instance.get<ThemeCubit>().themeData;

extension TextStyleCopy on TextStyle? {
  TextStyle? copyColor(Color color) {
    return this?.copyWith(color: color);
  }
}

extension ThemeExtension on BuildContext {
  ThemeData get themeData => Theme.of(this);
}
