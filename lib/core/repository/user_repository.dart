import 'package:toii_mesh/core/service/user_service.dart';
import 'package:toii_mesh/model/base/base_response.dart';
import 'package:toii_mesh/model/user/user_model.dart';
 

abstract class UserRepository {
  Future<BaseResponse<UserModel>> getUserStats(String userId);
 
}

class UserRepositoryImpl extends UserRepository {
  final UserService userService;

  UserRepositoryImpl({required this.userService});

  @override
  Future<BaseResponse<UserModel>> getUserStats(String userId) async {
    try {
      return await userService.getUserStats(userId);
    } catch (e) {
      rethrow;
    }
  }
 
}
