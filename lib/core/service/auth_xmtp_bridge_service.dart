import 'dart:developer';

import 'package:get_it/get_it.dart';
import 'package:toii_mesh/core/constant/key_shared.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/utils/shared_prefs/shared_prefs.dart';

/// Service that bridges authentication and XMTP initialization
/// Automatically initializes XMTP when user authenticates with wallet
class AuthXmtpBridgeService {
  static final AuthXmtpBridgeService _instance =
      AuthXmtpBridgeService._internal();
  factory AuthXmtpBridgeService() => _instance;
  AuthXmtpBridgeService._internal();

  bool _isInitialized = false;
  XmtpCubit? _xmtpCubit;

  /// Initialize the bridge service
  void initialize() {
    if (_isInitialized) return;

    _xmtpCubit = GetIt.instance<XmtpCubit>();
    _isInitialized = true;

    log('AuthXmtpBridgeService: Initialized');
  }

  /// Call this method after successful wallet authentication
  /// This will automatically initialize XMTP with the authenticated wallet
  Future<void> onWalletAuthenticated({
    required String walletAddress,
    bool forceReinitialize = false,
  }) async {
    try {
      if (_xmtpCubit == null) {
        log('AuthXmtpBridgeService: XmtpCubit not available, skipping auto-initialization');
        return;
      }

      log('AuthXmtpBridgeService: Wallet authenticated: $walletAddress');

      // Store the current wallet address for XMTP to use
      SharedPref.setString(KeyShared.walletCurrentKey, walletAddress);

      // Check if XMTP is already initialized for this wallet
      if (!forceReinitialize && _xmtpCubit!.hasClient) {
        final currentInboxId = _xmtpCubit!.inboxId;
        if (currentInboxId != null) {
          log('AuthXmtpBridgeService: XMTP already initialized with inbox: $currentInboxId');
          return;
        }
      }

      // Auto-initialize XMTP client with the authenticated wallet
      log('AuthXmtpBridgeService: Auto-initializing XMTP client...');
      await _xmtpCubit!.autoInitializeClient();

      log('AuthXmtpBridgeService: XMTP auto-initialization completed');
    } catch (e) {
      log('AuthXmtpBridgeService: Error during auto-initialization: $e');
    }
  }

  /// Call this method when user logs out
  /// This will cleanup XMTP client
  Future<void> onWalletDisconnected() async {
    try {
      if (_xmtpCubit == null) return;

      log('AuthXmtpBridgeService: Wallet disconnected, cleaning up XMTP...');

      // Clear current wallet
      await SharedPref.removeKey(KeyShared.walletCurrentKey);

      // Close XMTP client
      await _xmtpCubit!.close();

      log('AuthXmtpBridgeService: XMTP cleanup completed');
    } catch (e) {
      log('AuthXmtpBridgeService: Error during cleanup: $e');
    }
  }

  /// Check if XMTP should be auto-initialized
  Future<bool> shouldAutoInitializeXmtp() async {
    try {
      if (_xmtpCubit == null) return false;

      // Check if user is logged in
      final isLoggedIn = SharedPref.getBool(KeyShared.isLogin);
      if (!isLoggedIn) return false;

      // Check if wallet credentials are available
      return await _xmtpCubit!.canAutoInitialize();
    } catch (e) {
      log('AuthXmtpBridgeService: Error checking auto-initialization: $e');
      return false;
    }
  }

  /// Force reinitialize XMTP with current wallet
  Future<void> reinitializeXmtp() async {
    try {
      final currentWallet = SharedPref.getString(KeyShared.walletCurrentKey);
      if (currentWallet.isNotEmpty) {
        await onWalletAuthenticated(
          walletAddress: currentWallet,
          forceReinitialize: true,
        );
      }
    } catch (e) {
      log('AuthXmtpBridgeService: Error during reinitialize: $e');
    }
  }
}
