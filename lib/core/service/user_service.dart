import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:toii_mesh/model/base/base_response.dart';
import 'package:toii_mesh/model/user/user_model.dart';
 

part 'user_service.g.dart';

@RestApi()
abstract class UserService {
  factory UserService(Dio dio, {String baseUrl}) = _UserService;

  @GET('/user/api/v1/users/{user_id}/profile')
  Future<BaseResponse<UserModel>> getUserStats(@Path('user_id') String userId);
 
}
