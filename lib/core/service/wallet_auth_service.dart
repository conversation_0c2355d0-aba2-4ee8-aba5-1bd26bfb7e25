import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_mesh/core/constant/key_shared.dart';
import 'package:toii_mesh/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_mesh/model/credentials/credentials.dart';
import 'package:toii_mesh/model/user/user_model.dart';
import 'package:toii_mesh/utils/keychain/keychain_service.dart';
import 'package:toii_mesh/utils/shared_prefs/shared_prefs.dart';

/// Service for getting current user's wallet authentication credentials
class WalletAuthService {
  static final WalletAuthService _instance = WalletAuthService._internal();
  factory WalletAuthService() => _instance;
  WalletAuthService._internal();

  /// Get current user's wallet credentials for XMTP
  Future<WalletCredentials?> getCurrentWalletCredentials() async {
    try {
      // Try to get wallet address from multiple sources
      String? walletAddress = await _getCurrentWalletAddress();
      
      if (walletAddress == null || walletAddress.isEmpty) {
        log('WalletAuthService: No wallet address found');
        return null;
      }

      // Get private key for the wallet address
      String? privateKey = await KeychainService.instance
          .readPrivateKeyFromiCloud(walletAddress);
      
      if (privateKey == null || privateKey.isEmpty) {
        log('WalletAuthService: No private key found for address: $walletAddress');
        return null;
      }

      log('WalletAuthService: Successfully retrieved wallet credentials for: $walletAddress');
      return WalletCredentials(
        address: walletAddress,
        privateKey: privateKey,
      );
    } catch (e) {
      log('WalletAuthService: Error getting wallet credentials: $e');
      return null;
    }
  }

  /// Get current wallet address from various sources
  Future<String?> _getCurrentWalletAddress() async {
    try {
      // Method 1: Try to get from SharedPreferences (current wallet)
      String? currentWallet = SharedPref.getString(KeyShared.walletCurrentKey);
      if (currentWallet.isNotEmpty) {
        log('WalletAuthService: Found wallet address in SharedPref: $currentWallet');
        return currentWallet;
      }

      // Method 2: Try to get from user profile
      String? profileWallet = await _getWalletFromProfile();
      if (profileWallet != null && profileWallet.isNotEmpty) {
        log('WalletAuthService: Found wallet address in profile: $profileWallet');
        return profileWallet;
      }

      // Method 3: Try to get from keychain profiles
      String? keychainWallet = await _getWalletFromKeychain();
      if (keychainWallet != null && keychainWallet.isNotEmpty) {
        log('WalletAuthService: Found wallet address in keychain: $keychainWallet');
        return keychainWallet;
      }

      log('WalletAuthService: No wallet address found in any source');
      return null;
    } catch (e) {
      log('WalletAuthService: Error getting current wallet address: $e');
      return null;
    }
  }

  /// Get wallet address from user profile
  Future<String?> _getWalletFromProfile() async {
    try {
      // Try to get from ProfileCubit if available
      final profileCubit = GetIt.instance.get<ProfileCubit>();
      final profileState = profileCubit.state;
      
      if (profileState.userModel != null) {
        return profileState.userModel!.walletAddress ?? 
               profileState.userModel!.address;
      }
      
      return null;
    } catch (e) {
      log('WalletAuthService: Error getting wallet from profile: $e');
      return null;
    }
  }

  /// Get wallet address from keychain profiles
  Future<String?> _getWalletFromKeychain() async {
    try {
      final profileData = await KeychainService.instance.getProfile();
      
      if (profileData.isNotEmpty) {
        // Get the first available wallet address from profiles
        for (final entry in profileData.entries) {
          final userModel = UserModel.fromJson(entry.value);
          final walletAddress = userModel.walletAddress ?? userModel.address;
          if (walletAddress != null && walletAddress.isNotEmpty) {
            return walletAddress;
          }
        }
      }
      
      return null;
    } catch (e) {
      log('WalletAuthService: Error getting wallet from keychain: $e');
      return null;
    }
  }

  /// Check if user has valid wallet credentials
  Future<bool> hasValidWalletCredentials() async {
    final credentials = await getCurrentWalletCredentials();
    return credentials != null;
  }

  /// Get list of available wallet addresses
  Future<List<String>> getAvailableWalletAddresses() async {
    try {
      return await KeychainService.instance.getListWallet();
    } catch (e) {
      log('WalletAuthService: Error getting available wallets: $e');
      return [];
    }
  }
}

/// Model for wallet credentials
class WalletCredentials {
  final String address;
  final String privateKey;

  const WalletCredentials({
    required this.address,
    required this.privateKey,
  });

  /// Convert to Credentials model
  Credentials toCredentials() {
    return Credentials(
      address: address,
      privateKeyHex: privateKey,
    );
  }

  @override
  String toString() {
    return 'WalletCredentials(address: $address, privateKey: [HIDDEN])';
  }
}
